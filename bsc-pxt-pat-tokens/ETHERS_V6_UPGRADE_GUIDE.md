# Ethers v5 到 v6 升级指南

## 📋 升级概述

本项目已成功从 ethers v5 升级到 v6。以下是主要变更和升级步骤的详细说明。

## 🔄 主要变更

### 1. 依赖更新

**package.json 变更：**
```json
// 旧版本 (v5)
"@nomiclabs/hardhat-ethers": "^2.2.3",
"@nomiclabs/hardhat-etherscan": "^3.1.7", 
"@nomiclabs/hardhat-waffle": "^2.0.6",
"ethers": "^5.8.0",

// 新版本 (v6)
"@nomicfoundation/hardhat-ethers": "^3.0.8",
"@nomicfoundation/hardhat-verify": "^2.0.11",
"@nomicfoundation/hardhat-chai-matchers": "^2.0.8",
"ethers": "^6.13.4",
```

### 2. Hardhat 配置更新

**hardhat.config.js 变更：**
```javascript
// 旧版本
require("@nomiclabs/hardhat-waffle");
require("@nomiclabs/hardhat-etherscan");
require("@nomiclabs/hardhat-ethers");

// 新版本
require("@nomicfoundation/hardhat-ethers");
require("@nomicfoundation/hardhat-verify");
require("@nomicfoundation/hardhat-chai-matchers");
```

### 3. 代码语法变更

#### BigNumber 处理
```javascript
// v5
ethers.utils.parseEther("1.0")
ethers.utils.formatEther(balance)

// v6
ethers.parseEther("1.0")
ethers.formatEther(balance)
```

#### Provider 创建
```javascript
// v5
new ethers.providers.JsonRpcProvider(url)

// v6
new ethers.JsonRpcProvider(url)
```

#### 合约部署
```javascript
// v5
await contract.deployed()
contract.address

// v6
await contract.waitForDeployment()
await contract.getAddress()
```

#### 余额查询
```javascript
// v5
await ethers.provider.getBalance(address)

// v6
await signer.provider.getBalance(address)
```

#### BigNumber 运算
```javascript
// v5
balance.add(amount)
balance.sub(amount)
balance.eq(amount)
balance.lt(amount)
balance.gte(amount)
balance.div(divisor)

// v6
balance + amount
balance - amount
balance === amount
balance < amount
balance >= amount
balance / divisor
```

## 📁 已更新的文件

### 配置文件
- ✅ `package.json` - 依赖版本更新
- ✅ `hardhat.config.js` - 插件和任务更新

### 部署脚本
- ✅ `scripts/deploy/01-deploy-core-tokens.js`
- ✅ `scripts/deploy/02-deploy-staking-system.js`

### 工具脚本
- ✅ `scripts/utils/check-balances.js`
- ✅ `scripts/utils/fund-cross-chain-pool.js`
- ✅ `scripts/utils/address-manager.js`

### 测试脚本
- ✅ `scripts/test/01-basic-function-test-safe.js`
- ✅ `scripts/test/05-bridge-test-fixed.js`
- ✅ `scripts/test/test-bidirectional-bridge.js`

### 查询脚本
- ✅ `scripts/query/check-bsc-bridge-status.js`

### 跨链脚本
- ✅ `scripts/cross-chain/check-chains-connection.js`

## 🚀 升级后的验证步骤

### 1. 安装新依赖
```bash
npm install
```

### 2. 编译合约
```bash
npm run compile
```

### 3. 运行基础测试
```bash
npm run test:basic
```

### 4. 检查账户余额
```bash
npm run check:balances
```

### 5. 验证部署功能
```bash
npm run deploy:core
```

## ⚠️ 注意事项

1. **BigNumber 运算**: v6 中 BigNumber 运算使用原生 JavaScript 运算符
2. **Provider 访问**: 需要通过 signer.provider 访问 provider
3. **合约地址**: 使用 `getAddress()` 方法获取合约地址
4. **部署等待**: 使用 `waitForDeployment()` 替代 `deployed()`

## 🔧 故障排除

### 常见错误及解决方案

1. **TypeError: ethers.utils is undefined**
   - 解决: 使用 `ethers.parseEther()` 替代 `ethers.utils.parseEther()`

2. **TypeError: contract.address is undefined**
   - 解决: 使用 `await contract.getAddress()` 替代 `contract.address`

3. **TypeError: Cannot read property 'getBalance' of undefined**
   - 解决: 使用 `signer.provider.getBalance()` 替代 `ethers.provider.getBalance()`

## 📚 参考资源

- [Ethers v6 迁移指南](https://docs.ethers.org/v6/migrating/)
- [Hardhat Ethers 插件文档](https://hardhat.org/hardhat-runner/plugins/nomicfoundation-hardhat-ethers)
- [Ethers v6 API 文档](https://docs.ethers.org/v6/)

## ✅ 升级完成

所有文件已成功升级到 ethers v6。项目现在使用最新的 ethers 库和相关工具。
