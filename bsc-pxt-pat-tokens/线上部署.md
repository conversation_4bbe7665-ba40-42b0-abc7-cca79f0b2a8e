j<PERSON><PERSON><PERSON><PERSON><PERSON>@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network bscTestnet

=== 开始部署核心代币系统 ===
网络: bscTestnet
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署账户余额: 1.670422266398265 ETH

=== 创建池子账户 ===
中国大陆池子地址: ******************************************
国际池子地址: ******************************************
质押池地址: ******************************************
跨链池地址: ******************************************

=== 1. 部署 PXT 治理代币 ===
✅ PXT代币部署成功: ******************************************

=== 2. 部署 PAT 功能代币 ===
✅ PAT代币部署成功: ******************************************

=== 3. 部署代币注册表 ===
✅ 代币注册表部署成功: ******************************************

=== 4. 配置代币注册表 ===
注册表合约owner: ******************************************
部署者地址: ******************************************
是否为owner: true
正在添加工厂权限...
✅ 部署者已添加为工厂
验证工厂权限: true
准备注册PXT代币:
- 地址: ******************************************
- 名称: Platform Governance Token
- 符号: PXT
- 小数位: 18
- 总供应量: 100000000.0
✅ PXT代币已注册到注册表
准备注册PAT代币:
- 地址: ******************************************
- 名称: PX Activity Token
- 符号: PAT
- 小数位: 18
- 总供应量: 300000000.0
✅ PAT代币已注册到注册表

=== 5. 验证部署结果 ===
PXT总供应量: 100000000.0
PAT总供应量: 300000000.0
部署者PXT余额: 55002000.0
部署者PAT余额: 0.0

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/deployments/bscTestnet/core-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/02-deploy-staking-system.js --network bscTestnet

=== 开始部署质押系统 ===
网络: bscTestnet
链ID: 97
部署账户: ******************************************
✅ 已加载核心合约地址
- PXT代币: ******************************************
- PAT代币: ******************************************
- 代币注册表: ******************************************
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署质押工厂 ===
✅ 质押工厂部署成功: 0x4109CD858791Fa95C88564AfBD4AEA42258E7eFC

=== 2. 部署质押池 ===
✅ 质押池部署成功: ******************************************

=== 3. 初始化质押池 ===
✅ 质押池初始化完成

=== 4. 部署奖励分配器 ===
✅ 奖励分配器部署成功: 0x13FB8474A32E00beCeFB487D3bb131e5aa5bc966

=== 5. 配置质押系统关系 ===
✅ 质押池已添加到奖励分配器
✅ 已设置奖励分配器
✅ 已设置质押池实现合约

=== 6. 质押等级已预配置 ===
质押等级在合约构造函数中已预设：
- 丁级: 100 PXT
- 丙级: 1,000 PXT
- 乙级: 5,000 PXT
- 甲级: 20,000 PXT
- 十绝: 100,000 PXT
- 双十绝: 250,000 PXT
- 至尊: 500,000 PXT

=== 7. 验证质押系统 ===
最小质押金额: 1.0 PXT
基础年化收益率: 500 基点

=== 质押系统部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/deployments/bscTestnet/staking-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/03-deploy-governance.js --network bscTestnet

=== 开始部署治理系统 ===
网络: bscTestnet
链ID: 97
部署账户: ******************************************
调试 - 质押部署数据: {
  "network": "bscTestnet",
  "chainId": 97,
  "timestamp": "2025-07-29T02:41:28.425Z",
  "deployer": "******************************************",
  "treasury": "******************************************",
  "contracts": {
    "StakingFactory": "0x4109CD858791Fa95C88564AfBD4AEA42258E7eFC",
    "StakingPool": "******************************************",
    "RewardDistributor": "0x13FB8474A32E00beCeFB487D3bb131e5aa5bc966"
  },
  "configuration": {
    "minStakeAmount": "1000000000000000000",
    "baseAPY": "500",
    "stakeLevels": 7
  }
}
调试 - 读取的质押池地址: ******************************************
✅ 已加载依赖合约地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************
质押池地址: ******************************************

=== 1. 部署投票合约 ===
✅ 投票合约部署成功: 0x94EbC99Fe8a33ba6b9cf111c2a2b2ED41b959E71
✅ 投票合约初始化完成

=== 2. 部署提案管理器 ===
✅ 提案管理器部署成功: 0x6b5667865Df3EA0FBC021C87945FD76825557356
✅ 提案管理器初始化完成

=== 3. 部署DAO主合约 ===
✅ DAO主合约部署成功: ******************************************
✅ DAO主合约初始化完成

=== 4. 部署国库 ===
✅ 国库合约部署成功: ******************************************

=== 5. 配置治理系统关系 ===
✅ Treasury DAO地址更新完成
✅ 提案管理器配置完成

=== 6. 验证治理系统 ===
投票合约PXT代币地址: ******************************************
投票合约质押池地址: ******************************************
最低参与率: 10 %

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/deployments/bscTestnet/governance-deployment.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/04-deploy-content-system.js --network bscTestnet

=== 开始部署内容上链系统 ===
网络: bscTestnet
链ID: 97
部署账户: ******************************************
部署者余额: 1.056327386398265 ETH
✅ 已加载依赖合约地址
- PAT代币: ******************************************
- 国库合约: ******************************************

=== 1. 部署ContentRegistry合约 ===
正在部署ContentRegistry...
✅ ContentRegistry部署成功: ******************************************
验证ContentRegistry配置...
  PAT代币地址: ******************************************
  国库地址: ******************************************

=== 2. 部署ContentCharacter合约 ===
正在部署ContentCharacter...
✅ ContentCharacter部署成功: ******************************************

=== 3. 部署ContentMint合约 ===
正在部署ContentMint...
✅ ContentMint部署成功: ******************************************
验证ContentMint配置...
  ContentRegistry地址: ******************************************
  国库地址: ******************************************

=== 4. 测试基础功能 ===
测试内容类型费用...
  视频费用: 1.0 PAT
  文章费用: 0.05 PAT
测试统计信息...
  总内容数: 0
  活跃内容数: 0
  总PAT消耗: 0.0 PAT

=== 5. 生成验证命令 ===
ContentRegistry验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"
ContentCharacter验证命令:
  npx hardhat verify --network bscTestnet ******************************************
ContentMint验证命令:
  npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"

=== 6. 保存部署信息 ===
✅ 部署信息已保存到: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/deployments/bscTestnet/content-deployment.json

=== 内容上链系统部署完成 ===
ContentRegistry: ******************************************
ContentCharacter: ******************************************
ContentMint: ******************************************

jzy:bsc-pxt-pat-tokens junziliuyi$ npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network bscTestnet
🌉 开始部署BSC端TokenBridge合约
================================================
网络: bscTestnet
链ID: 97
时间: 2025-07-30T03:04:25.918Z
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
✅ 已加载核心代币地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署TokenBridge合约 ===
✅ TokenBridge部署成功: 0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67

=== 2. 设置管理员权限 ===
✅ 已设置部署者为管理员

=== 3. 配置PXPAC链支持 ===
✅ 已设置PXPAC链名称: PXPAC
正在添加PXPAC链支持...
等待交易确认...
交易确认成功，区块: 60015169
✅ 已添加PXPAC链支持
  链ID: 11
  验证者数量: 3
  需要确认数: 2
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 4. 验证配置 ===
PXPAC链支持状态: ✅ 支持
PXPAC链验证者数量: 3
验证者地址:
  1. ******************************************
  2. ******************************************
  3. ******************************************
PXPAC链费用配置:
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/deployments/bscTestnet/bridge-deployment.json

📋 重要信息:
TokenBridge地址: 0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67
支持的目标链: PXPAC (链ID: 11)
验证者数量: 3
需要确认数: 2
费用接收地址: ******************************************

🔧 下一步:
1. 配置跨链桥连接: npx hardhat run scripts/bridge/setup-bridge-connection.js --network bscTestnet
2. 测试跨链功能: npx hardhat run scripts/test/05-bridge-test.js --network bscTestnet
jzy:bsc-pxt-pat-tokens junziliuyi$ 

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/test/01-basic-function-test-safe.js --network bscTestnet
🧪 开始基础功能测试（安全版本）
================================================
网络: bscTestnet
时间: 2025-07-29T05:39:27.805Z
✅ 已加载部署信息
- PXT代币: ******************************************
- PAT代币: ******************************************
- 质押池: ******************************************
测试账户:
- 部署者: ******************************************
- 用户1: ******************************************
- 用户2: ******************************************

=== 1. 代币基础信息测试 ===
PXT代币信息:
- 名称: Platform Governance Token
- 符号: PXT
- 精度: 18
- 总供应量: 100000000.0

PAT代币信息:
- 名称: PX Activity Token
- 符号: PAT
- 精度: 18
- 总供应量: 300000000.0

=== 2. 代币余额测试 ===
部署者余额:
- PXT: 55000000.0
- PAT: 0.0

=== 3. 代币转账测试 ===
转账1000 PXT给用户1...
✅ 用户1 PXT余额: 15002700.0

=== 4. 代币授权测试 ===
用户1授权部署者使用500 PXT...
✅ 授权额度: 500.0
部署者代表用户1转账100 PXT给用户2...
✅ 用户1新余额: 15002600.0
✅ 用户2 PXT余额: 10000300.0

=== 5. 质押系统基础测试 ===
检查质押池初始化状态...
- PXT代币地址: ******************************************
- 奖励代币地址: ******************************************
质押池信息:
- 最小质押量: 1.0 PXT
- 基础年化收益率: 500 基点
- 总质押量: 100.0 PXT

=== 6. PAT池子余额检查 ===
PAT池子余额:
- 中国大陆池: 100000000.0 PAT
- 国际池: 100000000.0 PAT
- 跨链池: 100000000.0 PAT
- 池子总余额: 300000000.0 PAT

=== 测试完成 ===
✅ 基础功能测试完成
💡 如果遇到RPC限制错误，这是正常的，说明测试网有请求频率限制
💡 主要功能都已验证正常工作
📄 测试结果已保存: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/test-results/basic-function-test-bscTestnet-1753767598758.json
junziliuyi@jzy bsc-pxt-pat-tokens % 

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/test/02-reward-system-test.js --network bscTestnet
💰 开始奖励系统测试
================================================
网络: bscTestnet
时间: 2025-07-29T05:58:05.286Z
测试账户:
- 部署者: ******************************************
- 用户1: ******************************************
- 用户2: ******************************************
- 用户3: ******************************************
✅ 已加载部署信息
- PXT代币: ******************************************
- PAT代币: ******************************************
- 质押池: ******************************************
- 质押工厂: 0x4109CD858791Fa95C88564AfBD4AEA42258E7eFC
- 奖励分配器: 0x13FB8474A32E00beCeFB487D3bb131e5aa5bc966

📋 合约信息:
- PXT代币: ******************************************
- PAT代币: ******************************************
- 质押池: ******************************************
- 质押工厂: 0x4109CD858791Fa95C88564AfBD4AEA42258E7eFC
- 奖励分配器: 0x13FB8474A32E00beCeFB487D3bb131e5aa5bc966

=== 1. 检查质押池状态 ===
质押池配置:
- PXT代币地址: ******************************************
- 奖励代币地址: ******************************************
- 基础年化收益率: 500 基点
- 最小质押金额: 1.0 PXT
- 总质押量: 1100.0 PXT

质押等级阈值:
- 丁级: 1000.0 PXT
- 丙级: 5000.0 PXT
- 乙级: 20000.0 PXT
- 甲级: 100000.0 PXT
- 十绝: 250000.0 PXT
- 双十绝: 500000.0 PXT
- 至尊: 0.0 PXT

=== 2. 检查奖励分配器状态 ===
奖励分配器配置:
- 奖励代币地址: ******************************************
- 质押池分配点数: 1000
- 总分配量: 0.0 PAT
- 总分发量: 0.0 PAT
- 最后奖励时间: 2025/7/29 10:41:28
✅ 奖励分配器状态检查完成

=== 3. 准备测试代币 ===
分发测试代币给用户...
用户1 PXT余额: 15021600.0
用户2 PXT余额: 10010300.0
用户3 PXT余额: 20000.0

=== 4. 测试质押功能 ===
用户1质押测试...
授权质押池使用PXT...
✅ 已授权质押池使用PXT
授权额度: 1000.0 PXT
执行质押操作...
✅ 质押成功，交易哈希: 0x583cbf277fb547fb376321912f4d64efcb0ee05be665f5d34fffe545042fcc13
质押信息:
  质押金额: 2100.0 PXT
  质押等级: 1 (丁级)
  开始时间: 2025/7/29 13:21:57
  是否解锁中: false
  待领取奖励: 0.001448919599822421 PAT
质押池总量: 2100.0 PXT

=== 5. 测试奖励计算 ===
用户1质押信息:
- 质押金额: 2100.0 PXT
- 质押等级: 1
- 待领取奖励: 0.001448919599822421 PAT
计算的奖励: 0.000013895119863012 PAT

质押等级判定测试:
- 100.0 PXT -> 丁级 (等级1)
- 1000.0 PXT -> 丙级 (等级2)
- 5000.0 PXT -> 乙级 (等级3)
- 20000.0 PXT -> 甲级 (等级4)
- 100000.0 PXT -> 十绝 (等级5)
- 250000.0 PXT -> 双十绝 (等级6)
- 500000.0 PXT -> 至尊 (等级7)

=== 6. 时间推进测试 ===
推进时间1小时...
⚠️  时间推进测试跳过: The method evm_increaseTime does not exist/is not available

==================================================
🎉 奖励系统测试完成!
==================================================
📄 测试报告已保存: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/test-reports/reward-system-test-bscTestnet-1753768705449.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/test/03-governance-test.js --network bscTestnet
🏛️ 开始治理系统测试
================================================
网络: bscTestnet
时间: 2025-07-29T06:10:50.981Z
测试账户:
- 部署者: ******************************************
- 用户1: ******************************************
- 用户2: ******************************************
- 用户3: ******************************************
✅ 已加载部署信息
- PXT代币: ******************************************
- 质押池: ******************************************
- DAO合约: ******************************************
- 提案管理器: 0x6b5667865Df3EA0FBC021C87945FD76825557356
- 投票合约: 0x94EbC99Fe8a33ba6b9cf111c2a2b2ED41b959E71
- 国库合约: ******************************************

📋 治理合约信息:
- DAO: ******************************************
- 提案管理器: 0x6b5667865Df3EA0FBC021C87945FD76825557356
- 投票合约: 0x94EbC99Fe8a33ba6b9cf111c2a2b2ED41b959E71
- 国库合约: ******************************************

=== 1. 准备治理参与者 ===
用户当前PXT余额:
- 用户1: 15020600.0 PXT
- 用户2: 10020300.0 PXT
- 用户3: 20000.0 PXT
给用户3转账PXT...
✅ 用户PXT余额准备完成

准备用户质押...
✅ 用户1已有质押: 2100.0 PXT
用户2进行质押...
用户2当前余额: 10020300.0 PXT
✅ 用户2授权完成
用户2授权额度: 1000.0 PXT
✅ 用户2质押完成
用户3进行质押...
用户3当前余额: 70000.0 PXT
✅ 用户3授权完成
用户3授权额度: 1000.0 PXT
✅ 用户3质押完成

最终质押状态:
- 用户1质押: 2100.0 PXT
- 用户2质押: 1000.0 PXT
- 用户3质押: 1000.0 PXT
用户1质押等级: 1 (丁级)
✅ 用户准备阶段完成

=== 2. 创建治理提案 ===
当前区块时间: 1753769476
提案开始时间: 1753769536
提案结束时间: 1753769836
创建提案...
✅ 提案创建成功，ID: 1
提案标题: 测试治理提案

=== 3. 等待投票开始 ===
⚠️  测试网不支持时间推进，跳过时间相关测试
💡 在真实环境中，需要等待实际时间到达投票开始时间
💡 投票将在 2025/7/29 14:12:16 开始
提案状态: 0

=== 4. 模拟投票过程 ===
💡 在实际环境中，用户可以通过以下方式投票:
- 赞成票: proposalManager.vote(proposalId, 1)
- 反对票: proposalManager.vote(proposalId, 0)
- 弃权票: proposalManager.vote(proposalId, 2)

=== 5. 治理系统统计 ===
总提案数: 1
用户1投票权重: 2730.0
用户2投票权重: 1300.0
用户3投票权重: 1300.0
最低参与率要求: 10%

==================================================
🎉 治理系统测试完成!
==================================================
📄 测试报告已保存: /Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/test-reports/governance-test-bscTestnet-*************.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/test/fund-accounts.js --network bscTestnet
🏦 开始为测试账户充值 BNB
================================================
网络: bscTestnet
部署者账户: ******************************************
部署者 BNB 余额: 0.*************** BNB

=== 开始充值 ===

💰 为 中国大陆池 充值 0.05 BNB...
目标地址: ******************************************
当前余额: 0.01 BNB
交易哈希: 0x03da6e0c4a6798d5b4f70ddbcf596f43a1565c1e226733b636a8c9597cf952aa
⏳ 等待交易确认...
✅ 充值完成！新余额: 0.01 BNB

💰 为 跨链池 充值 0.05 BNB...
目标地址: ******************************************
当前余额: 0.01 BNB
交易哈希: 0xfe196615ce9afde7fd0751907a3b9ea112457f49567de1d353e267fd29771556
⏳ 等待交易确认...
✅ 充值完成！新余额: 0.01 BNB

=== 充值完成 ===
部署者最终余额: 0.*************** BNB

🎉 充值完成！

jzy:bsc-pxt-pat-tokens junziliuyi$ node scripts/test/05-bridge-test-fixed.js
🌉 BSC到PXPAC跨链桥功能测试（修复版）
================================================
时间: 2025-07-29T07:17:30.320Z
✅ 已加载部署信息
- TokenBridge: 0x7B996E8C53892EFD25B88d7aE1a4482A674c5f89
- PAT代币: ******************************************
- PXT代币: ******************************************
测试账户:
- 跨链池地址: ******************************************

=== 1. 检查跨链桥状态 ===
PXPAC链支持状态: ✅ 支持
验证者数量: 3
验证者地址: [
  '******************************************',
  '******************************************',
  '******************************************'
]
需要确认数: 2
基础费用: 0.001 BNB
百分比费用: 0.5 %

=== 2. 检查账户余额 ===
跨链池PAT余额: 99999900.0 PAT
跨链池BNB余额: 0.25489439 BNB

=== 3. 计算跨链费用 ===
跨链费用: 0.1 BNB

=== 4. 授权TokenBridge使用PAT ===
当前授权额度: 100.0 PAT
✅ 授权额度充足

=== 5. 执行跨链锁定 ===
锁定前状态:
- 跨链池PAT余额: 99999900.0 PAT
- 跨链桥PAT余额: 100.0 PAT
🚀 开始执行跨链锁定...
- 锁定数量: 100.0 PAT
- 目标链ID: 11
- 接收地址: ******************************************
- 支付费用: 0.1 BNB
⏳ 等待交易确认...
交易哈希: 0x78a46dcf859430d59ce1defb8974cf0851e984ed713ea0da13e48f7f89d23656
✅ 跨链锁定交易成功
Gas使用: 369945

=== 6. 验证锁定结果 ===
⏳ 等待区块链状态更新...
锁定后状态:
- 跨链池PAT余额: 99999800.0 PAT
- 跨链桥PAT余额: 200.0 PAT
余额变化:
- 跨链池减少: 100.0 PAT
- 跨链桥增加: 100.0 PAT
✅ 余额变化正确

=== 7. 跨链流程说明 ===
💡 跨链流程:
1. ✅ BSC链上锁定PAT代币
2. 🔄 验证者监听并验证锁定事件
3. 🎯 验证者在PXPAC链上确认并铸造wPAT
4. 🎉 用户在PXPAC链上收到对应的wPAT代币

📊 测试结果汇总:
🎊 BSC到PXPAC跨链桥测试成功！
✅ PAT代币锁定成功
✅ 跨链请求已创建
✅ 余额变化正确
✅ 费用计算正确

🔗 相关链接:
- 交易哈希: https://testnet.bscscan.com/tx/0x78a46dcf859430d59ce1defb8974cf0851e984ed713ea0da13e48f7f89d23656
- TokenBridge合约: https://testnet.bscscan.com/address/0x7B996E8C53892EFD25B88d7aE1a4482A674c5f89

🎉 跨链桥测试完成！

junziliuyi@jzy bsc-pxt-pat-tokens % node scripts/test/content-registry-test.js

🌐 开始BSC链内容注册测试（无IPFS版本）
================================================
时间: 2025-07-29T13:29:40.428Z
✅ 已加载部署信息
- PAT代币: ******************************************
- 内容注册器: ******************************************
测试账户: ******************************************

=== 1. 准备测试环境 ===
用户PAT余额: 99999998.0 PAT
当前授权额度: 115792089237316195423570985008687907853269984665640564039457.584007913129639935 PAT
✅ 授权额度充足

=== 2. 检查内容注册系统 ===
激活的内容类型: [
  'video',
  'novel',
  'short_drama',
  'anime',
  'manga',
  'music',
  'article',
  'short_video'
]
video内容费用: 1.0 PAT
当前内容总数: 2

=== 3. 注册测试内容到BSC链 ===
📝 注册内容到BSC链...
- 标题: BSC链智能合约开发指南_1753795785535
- 模拟IPFS哈希: QmBSCTestContent1753795785535
⏳ 等待交易确认...
交易哈希: 0xb748cbed949743236df2b20795683955c7fa99509876182656ea914d9eefd075
🎉 内容注册成功！
- 交易哈希: 0xb748cbed949743236df2b20795683955c7fa99509876182656ea914d9eefd075
- Gas使用: 382131
- 交易状态: 1
✅ 内容ID（通过计数获取）: 3

=== 4. 验证链上内容 ===
✅ 链上内容验证:
- 内容ID: 3
- 标题: BSC链智能合约开发指南_1753795785535
- 类型: video
- 创建者: ******************************************
- IPFS哈希: QmBSCTestContent1753795785535
- 元数据URI: QmBSCTestContent1753795785535
- PAT费用: 1.0 PAT
- 创建时间: 2025-07-29T13:29:49.000Z
- 是否激活: true
- 是否锁定: false
- 铸造次数: 0
- 总收益: 0.0 PAT
更新后内容总数: 3

=== 5. 检查用户余额变化 ===
用户最终PAT余额: 99999997.0 PAT
PAT余额变化: 1.0 PAT

=== 测试结果汇总 ===
🎊 BSC链内容注册测试成功！
✅ 内容已成功注册到BSC链
✅ 使用PAT代币支付费用
✅ 使用BNB支付Gas费用
✅ 内容信息完整存储在链上
✅ 支持审核者机制

📊 测试数据:
- 内容ID: 3
- 支付费用: 1.0 PAT
- Gas费用: 0.00382131 BNB

💡 下一步测试:
1. 测试内容铸造功能
2. 测试内容审核功能
3. 测试内容收益分配

🎉 测试完成！



junziliuyi@jzy bsc-pxt-pat-tokens % node scripts/test/05-bridge-test-fixed.js
🌉 BSC到PXPAC跨链桥功能测试（修复版）
================================================
时间: 2025-07-30T06:53:09.129Z
✅ 已加载部署信息
- TokenBridge: 0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67
- PAT代币: ******************************************
- PXT代币: ******************************************
测试账户:
- 跨链池地址: ******************************************

=== 1. 检查跨链桥状态 ===
PXPAC链支持状态: ✅ 支持
验证者数量: 3
验证者地址: [
  '******************************************',
  '******************************************',
  '******************************************'
]
需要确认数: 2
基础费用: 0.001 BNB
百分比费用: 0.5 %

=== 2. 检查账户余额 ===
跨链池PAT余额: 99999700.0 PAT
跨链池BNB余额: 0.19703491 BNB

=== 3. 计算跨链费用 ===
跨链费用: 0.1 BNB

=== 4. 授权TokenBridge使用PAT ===
当前授权额度: 0.0 PAT
授权额度不足，进行授权...
✅ 已授权TokenBridge使用 100.0 PAT
新授权额度: 100.0 PAT

=== 5. 执行跨链锁定 ===
锁定前状态:
- 跨链池PAT余额: 99999700.0 PAT
- 跨链桥PAT余额: 0.0 PAT
🚀 开始执行跨链锁定...
- 锁定数量: 100.0 PAT
- 目标链ID: 11
- 接收地址: ******************************************
- 支付费用: 0.1 BNB
⏳ 等待交易确认...
交易哈希: 0xdd69164e9bde52e46bf6150afdaa3e1bb24bab06b07f0eaff77b7e182682ec55
✅ 跨链锁定交易成功
Gas使用: 418445

=== 6. 验证锁定结果 ===
⏳ 等待区块链状态更新...
锁定后状态:
- 跨链池PAT余额: 99999600.0 PAT
- 跨链桥PAT余额: 100.0 PAT
余额变化:
- 跨链池减少: 100.0 PAT
- 跨链桥增加: 100.0 PAT
✅ 余额变化正确

=== 7. 跨链流程说明 ===
💡 跨链流程:
1. ✅ BSC链上锁定PAT代币
2. 🔄 验证者监听并验证锁定事件
3. 🎯 验证者在PXPAC链上确认并铸造wPAT
4. 🎉 用户在PXPAC链上收到对应的wPAT代币

📊 测试结果汇总:
🎊 BSC到PXPAC跨链桥测试成功！
✅ PAT代币锁定成功
✅ 跨链请求已创建
✅ 余额变化正确
✅ 费用计算正确

🔗 相关链接:
- 交易哈希: https://testnet.bscscan.com/tx/0xdd69164e9bde52e46bf6150afdaa3e1bb24bab06b07f0eaff77b7e182682ec55
- TokenBridge合约: https://testnet.bscscan.com/address/0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67

🔧 下一步:
1. 在PXPAC链上验证跨链接收
2. 检查PXPAC链上的wPAT余额
3. 测试反向跨链（PXPAC → BSC）

🎉 跨链桥测试完成！
junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network bscTestnet
🔍 检查跨链池余额状态
================================================
网络: bscTestnet
跨链池地址: ******************************************
PAT代币地址: ******************************************

=== 检查余额 ===
检查失败: Error: call revert exception [ See: https://links.ethers.org/v5-errors-CALL_EXCEPTION ] (method="balanceOf(address)", data="0x", errorArgs=null, errorName=null, errorSignature=null, reason=null, code=CALL_EXCEPTION, version=abi/5.8.0)
    at Logger.makeError (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/logger/src.ts/index.ts:269:28)
    at Logger.throwError (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/logger/src.ts/index.ts:281:20)
    at Interface.decodeFunctionResult (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/abi/src.ts/interface.ts:427:23)
    at Contract.<anonymous> (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/contracts/src.ts/index.ts:400:44)
    at step (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/contracts/lib/index.js:48:23)
    at Object.next (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/contracts/lib/index.js:29:53)
    at fulfilled (/Users/<USER>/Desktop/PXPAC/bsc-pxt-pat-tokens/node_modules/@ethersproject/contracts/lib/index.js:20:58)
    at processTicksAndRejections (node:internal/process/task_queues:105:5) {
  reason: null,
  code: 'CALL_EXCEPTION',
  method: 'balanceOf(address)',
  data: '0x',
  errorArgs: null,
  errorName: null,
  errorSignature: null,
  address: '******************************************',
  args: [ '******************************************' ],
  transaction: {
    data: '0x70a08231000000000000000000000000a28e20bb2b6495fbe0eb2173ea323f7af3d378c9',
    to: '******************************************',
    from: '******************************************',
    gasLimit: BigNumber { value: "7022680" }
  }
}
junziliuyi@jzy bsc-pxt-pat-tokens % 




=== 1. 检查跨链桥状态 ===
PXPAC链支持状态: ✅ 支持
验证者数量: 3
验证者地址: [
  '******************************************',
  '******************************************',
  '******************************************'
]
需要确认数: 2
基础费用: 0.001 BNB
百分比费用: 0.5 %

=== 2. 检查账户余额 ===
跨链池PAT余额: 99999600.0 PAT
跨链池BNB余额: 0.09238988 BNB

=== 3. 计算跨链费用 ===
跨链费用: 0.1 BNB
❌ 跨链池BNB余额不足支付跨链费用
当前BNB余额: 0.09238988 BNB
需要BNB费用: 0.1 BNB
💡 解决方案: 给跨链池充值BNB
junziliuyi@jzy bsc-pxt-pat-tokens % clear
junziliuyi@jzy bsc-pxt-pat-tokens % node scripts/test/05-bridge-test-fixed.js
🌉 BSC到PXPAC跨链桥功能测试（修复版）
================================================
时间: 2025-07-30T11:42:22.263Z
✅ 已加载部署信息
- TokenBridge: 0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67
- PAT代币: ******************************************
- PXT代币: ******************************************
测试账户:
- 跨链池地址: ******************************************

=== 1. 检查跨链桥状态 ===
PXPAC链支持状态: ✅ 支持
验证者数量: 3
验证者地址: [
  '******************************************',
  '******************************************',
  '******************************************'
]
需要确认数: 2
基础费用: 0.001 BNB
百分比费用: 0.5 %

=== 2. 检查账户余额 ===
跨链池PAT余额: 99999600.0 PAT
跨链池BNB余额: 0.547894 BNB

=== 3. 计算跨链费用 ===
跨链费用: 0.1 BNB

=== 4. 授权TokenBridge使用PAT ===
当前授权额度: 0.0 PAT
授权额度不足，进行授权...
✅ 已授权TokenBridge使用 1000.0 PAT
新授权额度: 1000.0 PAT

=== 5. 执行跨链锁定 ===
锁定前状态:
- 跨链池PAT余额: 99999600.0 PAT
- 跨链桥PAT余额: 100.0 PAT
🚀 开始执行跨链锁定...
- 锁定数量: 1000.0 PAT
- 目标链ID: 11
- 接收地址: ******************************************
- 支付费用: 0.1 BNB
⏳ 等待交易确认...
交易哈希: 0xcf7fc9c93860e451cea4582c86d0a3459481dde7017b09bc8ad8a8b197aab321
✅ 跨链锁定交易成功
Gas使用: 369945

=== 6. 验证锁定结果 ===
⏳ 等待区块链状态更新...
锁定后状态:
- 跨链池PAT余额: 99998600.0 PAT
- 跨链桥PAT余额: 1100.0 PAT
余额变化:
- 跨链池减少: 1000.0 PAT
- 跨链桥增加: 1000.0 PAT
✅ 余额变化正确

=== 7. 跨链流程说明 ===
💡 跨链流程:
1. ✅ BSC链上锁定PAT代币
2. 🔄 验证者监听并验证锁定事件
3. 🎯 验证者在PXPAC链上确认并铸造wPAT
4. 🎉 用户在PXPAC链上收到对应的wPAT代币

📊 测试结果汇总:
🎊 BSC到PXPAC跨链桥测试成功！
✅ PAT代币锁定成功
✅ 跨链请求已创建
✅ 余额变化正确
✅ 费用计算正确

🔗 相关链接:
- 交易哈希: https://testnet.bscscan.com/tx/0xcf7fc9c93860e451cea4582c86d0a3459481dde7017b09bc8ad8a8b197aab321
- TokenBridge合约: https://testnet.bscscan.com/address/0x5F7d1d80d66af6DBd16C2cb95398605A794C8A67

🔧 下一步:
1. 在PXPAC链上验证跨链接收
2. 检查PXPAC链上的wPAT余额
3. 测试反向跨链（PXPAC → BSC）

🎉 跨链桥测试完成！
junziliuyi@jzy bsc-pxt-pat-tokens % 