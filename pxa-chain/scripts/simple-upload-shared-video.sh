#!/bin/bash

# PXPAC链分享视频内容上链脚本
# 用于上传分享的视频内容（非原创）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $CYAN "📤 PXPAC链分享视频内容上链脚本"
print_message $CYAN "======================================="
print_message $BLUE "📹 内容类型: 分享视频（非原创）"
print_message $BLUE "🔗 目标链: PXPAC Production Chain"
print_message $BLUE "💰 支付代币: PXPAC-T (wPAT)"
print_message $BLUE "📦 存储: Pinata IPFS"
print_message $CYAN "======================================="

# 配置
# 线上环境 (注释掉用于测试)
# BACKEND_URL="https://testbackend.pxpac.com"

# 本地测试环境
BACKEND_URL="http://localhost:8080"

# 内嵌分享视频数据
create_shared_video_data() {
    cat > /tmp/shared_video_data.json << 'EOF'
{
    "content_ksuid": "26gUHw3OGbzOLl2fcTvwO8fwRSR",
    "creator_ksuid": "2ztzcl8eDbf5jnsTc2M6bRnag2L",
    "title": "精彩科技纪录片片段合集",
    "description": "收集整理了一些优秀的科技类纪录片精彩片段，包括人工智能、区块链、量子计算等前沿技术内容。来源于各大知名纪录片制作方，仅供学习交流使用，已标注原创来源。",
    "type": "video",
    "status": "published",
    "rating": "A",
    "video_ID": "tech_doc_collection_001",
    "user_ksuid": "2ztzcl8eDbf5jnsTc2M6bRnag2L",
    "cover": "tech_documentary_collection_cover.jpg",
    "cover_url": "https://pxpac.com/content/covers/tech_documentary_collection.jpg",
    "play_url": "{\"1080p\":{\"path\":\"https://pxpac.com/content/shared/tech_doc_collection_1080p.mp4\",\"width\":1920,\"height\":1080},\"720p\":{\"path\":\"https://pxpac.com/content/shared/tech_doc_collection_720p.mp4\",\"width\":1280,\"height\":720}}",
    "preview_url": "https://pxpac.com/content/shared/tech_doc_collection_preview.mp4",
    "duration": 1856.2,
    "language": "zh-CN",
    "orientation": "landscape",
    "creation_time": "2025-07-24T14:00:00+08:00",
    "view_count": 0,
    "like_count": 0,
    "dislike_count": 0,
    "comment_count": 0,
    "share_count": 0,
    "favorite_count": 0,
    "original_tags": "科技纪录片,人工智能,区块链,量子计算,技术分享,教育内容",
    "category": {
        "id": 80,
        "name": "科技 / 教育",
        "slug": "keji-jiaoyu"
    },
    "has_collaborators": false,
    "all_approved": true,
    "content_source": "shared",
    "is_original": false,
    "sharing_type": "content_sharing",
    "source_platform": "多个纪录片制作方",
    "source_url": "https://www.documentaryheaven.com/tech-collection",
    "original_creator": "BBC科学纪录片团队等",
    "copyright_status": "shared_with_permission",
    "sharing_permission": {
        "has_permission": true,
        "permission_type": "educational_fair_use",
        "permission_note": "基于教育目的的合理使用，已标注原创来源"
    },
    "attribution": {
        "original_platform": "BBC, Discovery, National Geographic",
        "original_creator": "BBC科学纪录片团队, Discovery科技频道, 国家地理",
        "original_url": "https://www.documentaryheaven.com/tech-collection",
        "sharing_date": "2025-07-24T14:00:00+08:00"
    },
    "audit_info": {
        "status": "permit",
        "votes": 8,
        "permit_num": 8,
        "reject_num": 0,
        "auditor_logs": [
            {
                "user_ksuid": "2zMBqxYbpYkWIqRSYsN5Tojc7Xn",
                "vote_type": "permit",
                "vote_weight": 8,
                "reason": "科技教育内容质量优秀，已标注原创来源，符合教育分享规范，建议通过。",
                "created_at": "2025-07-24T14:30:00Z"
            }
        ]
    }
}
EOF
}

# 清理函数
cleanup() {
    if [ -f "/tmp/shared_video_data.json" ]; then
        rm -f /tmp/shared_video_data.json
    fi
}
trap cleanup EXIT

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        print_message $RED "❌ curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_message $RED "❌ jq 未安装，请运行: brew install jq"
        exit 1
    fi
    
    print_message $GREEN "✅ 依赖检查通过"
}

# 检查后端服务
check_backend() {
    print_message $BLUE "🔍 检查后端服务..."

    if ! curl -s "${BACKEND_URL}/health" > /dev/null 2>&1; then
        print_message $RED "❌ 后端服务不可用: ${BACKEND_URL}"
        print_message $YELLOW "💡 请确保后端服务已启动"
        exit 1
    fi

    print_message $GREEN "✅ 后端服务正常运行"
}

# 上链分享视频内容
upload_shared_video() {
    print_message $BLUE "📤 开始上链分享视频内容..."
    
    # 创建分享视频数据
    create_shared_video_data
    VIDEO_DATA_FILE="/tmp/shared_video_data.json"
    
    # 提取基本信息
    local title=$(jq -r '.title' $VIDEO_DATA_FILE)
    local description=$(jq -r '.description' $VIDEO_DATA_FILE)
    local content_type=$(jq -r '.type' $VIDEO_DATA_FILE)
    local content_ksuid=$(jq -r '.content_ksuid' $VIDEO_DATA_FILE)
    local user_ksuid=$(jq -r '.user_ksuid' $VIDEO_DATA_FILE)
    local is_original=$(jq -r '.is_original' $VIDEO_DATA_FILE)
    local content_source=$(jq -r '.content_source' $VIDEO_DATA_FILE)
    local source_platform=$(jq -r '.source_platform' $VIDEO_DATA_FILE)
    local original_creator=$(jq -r '.original_creator' $VIDEO_DATA_FILE)
    
    print_message $YELLOW "📋 分享视频信息:"
    print_message $YELLOW "   标题: $title"
    print_message $YELLOW "   内容ID: $content_ksuid"
    print_message $YELLOW "   分享者: $user_ksuid"
    print_message $YELLOW "   类型: $content_type"
    print_message $YELLOW "   是否原创: $is_original"
    print_message $YELLOW "   内容来源: $content_source"
    print_message $YELLOW "   原平台: $source_platform"
    print_message $YELLOW "   原创作者: $original_creator"
    
    # 构建完整的元数据（包含分享相关字段）
    local metadata=$(jq -c '{
        content_ksuid: .content_ksuid,
        user_ksuid: .user_ksuid,
        video_ID: .video_ID,
        duration: .duration,
        language: .language,
        orientation: .orientation,
        category: .category,
        rating: .rating,
        view_count: .view_count,
        like_count: .like_count,
        dislike_count: .dislike_count,
        comment_count: .comment_count,
        share_count: .share_count,
        favorite_count: .favorite_count,
        cover: .cover,
        cover_url: .cover_url,
        play_url: .play_url,
        preview_url: .preview_url,
        creation_time: .creation_time,
        original_tags: .original_tags,
        audit_info: .audit_info,
        has_collaborators: .has_collaborators,
        all_approved: .all_approved,
        content_source: .content_source,
        is_original: .is_original,
        sharing_type: .sharing_type,
        source_platform: .source_platform,
        source_url: .source_url,
        original_creator: .original_creator,
        copyright_status: .copyright_status,
        sharing_permission: .sharing_permission,
        attribution: .attribution,
        upload_timestamp: "'"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'",
        upload_source: "pxpac_chain_shared_script"
    }' $VIDEO_DATA_FILE)
    
    # 将视频数据转换为base64编码
    local file_data_base64=$(cat $VIDEO_DATA_FILE | base64 | tr -d '\n')

    # 构建API请求数据
    local request_data=$(cat <<EOF
{
    "content_ksuid": "$content_ksuid",
    "creator_ksuid": "$user_ksuid",
    "title": "$title",
    "description": "$description",
    "content_type": "$content_type",
    "file_data": "$file_data_base64",
    "file_name": "pxpac_shared_video_${content_ksuid}.json",
    "metadata": $metadata
}
EOF
    )
    
    print_message $BLUE "📤 发送分享内容上链请求..."
    print_message $YELLOW "🔍 调试: 请求URL: ${BACKEND_URL}/api/v1/content/real-upload"

    # 发送API请求
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST "${BACKEND_URL}/api/v1/content/real-upload" \
        -H "Content-Type: application/json" \
        -d "$request_data")

    local http_code=$(echo "$response" | tail -n1)
    local response_body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "202" ]; then
        print_message $GREEN "🎉 异步分享视频上链任务创建成功!"

        # 解析异步响应
        local success=$(echo "$response_body" | jq -r '.success // false')
        local task_id=$(echo "$response_body" | jq -r '.task_id // "N/A"')
        local content_ksuid=$(echo "$response_body" | jq -r '.content_ksuid // "N/A"')
        local status=$(echo "$response_body" | jq -r '.status // "N/A"')
        local message=$(echo "$response_body" | jq -r '.message // "N/A"')
        local estimated_time=$(echo "$response_body" | jq -r '.estimated_time // "N/A"')
        
        # 显示异步任务详情
        echo ""
        print_message $CYAN "📤 异步分享视频上链任务详情:"
        print_message $CYAN "=================================="
        print_message $GREEN "📹 视频标题: $title"
        print_message $GREEN "🔄 内容类型: 分享内容（非原创）"
        print_message $GREEN "🏷️ 原创作者: $original_creator"
        print_message $GREEN "🌐 来源平台: $source_platform"
        print_message $GREEN "🆔 任务ID: $task_id"
        print_message $GREEN "🔑 内容KSUID: $content_ksuid"
        print_message $GREEN "📊 当前状态: $status"
        print_message $GREEN "⏱️ 预计时间: $estimated_time"
        print_message $GREEN "💬 状态消息: $message"
        
        echo ""
        print_message $PURPLE "🔗 查询命令:"
        print_message $PURPLE "   查询任务状态: ./simple-upload-shared-video.sh --check-task $task_id"
        print_message $PURPLE "   查询内容状态: ./simple-upload-shared-video.sh --check-content $content_ksuid"

        echo ""
        print_message $PURPLE "🌐 API查询链接:"
        print_message $PURPLE "   任务状态: ${BACKEND_URL}/api/v1/tasks/$task_id"
        print_message $PURPLE "   内容状态: ${BACKEND_URL}/api/v1/tasks/content/$content_ksuid"

        echo ""
        print_message $GREEN "✅ 异步分享内容上链任务已创建! 正在后台处理中..."
        print_message $YELLOW "💡 请使用上述命令查询处理进度"
        
        return 0
    else
        print_message $RED "❌ 分享视频上链失败 (HTTP $http_code)"
        local error_message=$(echo "$response_body" | jq -r '.message // .error // "Unknown error"' 2>/dev/null)
        print_message $YELLOW "🔍 错误信息: $error_message"
        print_message $YELLOW "🔍 调试: 响应内容:"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        return 1
    fi
}

# 主函数
main() {
    check_dependencies
    check_backend

    print_message $BLUE "🚀 开始自动上链分享视频内容..."

    if upload_shared_video; then
        echo ""
        print_message $GREEN "🎊 分享视频内容上链流程完成!"
        print_message $YELLOW "📝 重要提醒: 此内容已标记为分享内容，包含完整的版权归属信息"
    else
        print_message $RED "❌ 分享视频内容上链失败"
        exit 1
    fi
}

# 任务状态查询函数
check_task_status() {
    local task_id="$1"
    local content_ksuid="$2"

    if [ -n "$task_id" ]; then
        echo ""
        print_message $CYAN "🔍 查询任务状态..."
        print_message $YELLOW "任务ID: $task_id"

        local status_response=$(curl -s "${BACKEND_URL}/api/v1/tasks/${task_id}")
        echo ""
        print_message $BLUE "📋 任务状态响应:"
        echo "$status_response" | jq '.' 2>/dev/null || echo "$status_response"

    elif [ -n "$content_ksuid" ]; then
        echo ""
        print_message $CYAN "🔍 根据内容ID查询任务状态..."
        print_message $YELLOW "内容KSUID: $content_ksuid"

        local status_response=$(curl -s "${BACKEND_URL}/api/v1/tasks/content/${content_ksuid}")
        echo ""
        print_message $BLUE "📋 任务状态响应:"
        echo "$status_response" | jq '.' 2>/dev/null || echo "$status_response"
    fi
}

# 使用说明函数
show_usage() {
    echo ""
    print_message $CYAN "📖 使用说明:"
    print_message $WHITE "1. 上链分享视频内容:"
    print_message $WHITE "   ./simple-upload-shared-video.sh"
    print_message $WHITE ""
    print_message $WHITE "2. 查询任务状态 (通过任务ID):"
    print_message $WHITE "   ./simple-upload-shared-video.sh --check-task <task_id>"
    print_message $WHITE ""
    print_message $WHITE "3. 查询任务状态 (通过内容KSUID):"
    print_message $WHITE "   ./simple-upload-shared-video.sh --check-content <content_ksuid>"
    print_message $WHITE ""
    print_message $WHITE "💡 提示: 本地测试环境 - ${BACKEND_URL}"
    print_message $WHITE "💡 切换到线上: 取消注释线上URL，注释本地URL"
}

# 处理命令行参数
if [ "$1" = "--check-task" ] && [ -n "$2" ]; then
    check_task_status "$2" ""
    exit 0
elif [ "$1" = "--check-content" ] && [ -n "$2" ]; then
    check_task_status "" "$2"
    exit 0
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_usage
    exit 0
elif [ $# -gt 0 ]; then
    print_message $RED "❌ 未知参数: $1"
    show_usage
    exit 1
fi

# 运行主函数
main "$@"
