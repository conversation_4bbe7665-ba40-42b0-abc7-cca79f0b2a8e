import Link from 'next/link';
import { EyeIcon } from '@heroicons/react/24/outline';
import { ContentRecord } from '@/hooks/useContentDetail';
import { getStatusInfo, getContentTypeLabel } from '@/utils/contentManageUtils';
import { ContentStatusIcon } from './ContentStatusIcon';
import { Locale } from '@/lib/i18n';

interface ContentRecordCardProps {
  record: ContentRecord;
  locale: Locale;
  formatDate: (dateString: string) => string;
  t: (key: string) => string;
}

export function ContentRecordCard({ 
  record, 
  locale, 
  formatDate, 
  t 
}: ContentRecordCardProps) {
  const statusInfo = getStatusInfo(record.status, t);

  return (
    <div className="bg-zinc-900/60 backdrop-blur-xl border border-zinc-700/50 rounded-lg sm:rounded-xl p-4 sm:p-6 hover:border-zinc-600/50 transition-colors">
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-4 mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-base sm:text-lg font-semibold text-white mb-2 line-clamp-2">
            {record.title}
          </h3>
          <p className="text-sm text-zinc-400 line-clamp-2 mb-3">
            {record.description}
          </p>
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color} ${statusInfo.borderColor} border`}>
            <ContentStatusIcon status={statusInfo.iconType} className="w-3 h-3" />
            {statusInfo.label}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4">
        <div>
          <span className="text-xs text-zinc-500 block mb-1">{t('fields.contentType')}</span>
          <span className="text-sm text-white">
            {getContentTypeLabel(record.content_type, t)}
          </span>
        </div>
        
        <div>
          <span className="text-xs text-zinc-500 block mb-1">{t('fields.publisher')}</span>
          <span className="text-sm text-white font-mono truncate block">
            {(record as any).publisher_ksuid ?
              `${(record as any).publisher_ksuid.slice(0, 10)}...${(record as any).publisher_ksuid.slice(-8)}` :
              record.publisher ?
                `${record.publisher.slice(0, 10)}...${record.publisher.slice(-8)}` :
                'N/A'
            }
          </span>
        </div>
        
        <div>
          <span className="text-xs text-zinc-500 block mb-1">{t('fields.createdAt')}</span>
          <span className="text-sm text-white">
            {formatDate(record.created_at)}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-zinc-500">
          ID: <span className="font-mono text-zinc-400">
            {record.on_chain_id && record.content_ksuid ?
              `${record.on_chain_id}-${record.content_ksuid.slice(0, 8)}...` :
              'N/A'
            }
          </span>
        </div>
        
        {record.content_ksuid && record.status === 'confirmed' ? (
          <Link
            href={`/${locale}/content/detail/${record.content_ksuid}`}
            className="inline-flex items-center gap-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
          >
            <EyeIcon className="w-4 h-4" />
            <span className="hidden sm:inline">{t('actions.view')}</span>
            <span className="sm:hidden">查看</span>
          </Link>
        ) : (
          <div className="inline-flex items-center gap-1 px-3 py-1.5 bg-amber-600/50 text-amber-200 rounded-lg text-sm cursor-not-allowed">
            <EyeIcon className="w-4 h-4" />
            <span className="hidden sm:inline">上链中...</span>
            <span className="sm:hidden">上链中</span>
          </div>
        )}
      </div>
    </div>
  );
}
