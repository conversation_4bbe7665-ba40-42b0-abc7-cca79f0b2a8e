import { 
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { ContentStats } from '@/hooks/useContentDetail';

interface ContentStatsCardsProps {
  stats: ContentStats | null;
  t: (key: string) => string;
  onRefreshStats?: () => void;
  actualRecordCount?: number;
}

export function ContentStatsCards({ stats, t, onRefreshStats, actualRecordCount }: ContentStatsCardsProps) {
  if (!stats) {
    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-zinc-900/60 backdrop-blur-xl border border-zinc-700/50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-zinc-700 rounded mb-2"></div>
              <div className="h-6 bg-zinc-700 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const statsCards = [
    {
      title: t('stats.total') || '總計',
      value: stats.total_count,
      icon: <DocumentTextIcon className="w-5 h-5 sm:w-6 sm:h-6 text-blue-400" />,
      color: 'text-blue-400'
    },
    {
      title: t('stats.confirmed') || '已確認',
      value: stats.confirmed_count,
      icon: <CheckCircleIcon className="w-5 h-5 sm:w-6 sm:h-6 text-green-400" />,
      color: 'text-green-400'
    },
    {
      title: t('stats.pending') || '待確認',
      value: stats.pending_count,
      icon: <ClockIcon className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-400" />,
      color: 'text-yellow-400'
    },
    {
      title: t('stats.failed') || '失敗',
      value: stats.failed_count,
      icon: <XCircleIcon className="w-5 h-5 sm:w-6 sm:h-6 text-red-400" />,
      color: 'text-red-400'
    }
  ];

  // 检查数据一致性
  const isInconsistent = actualRecordCount !== undefined && stats && stats.total_count !== actualRecordCount;

  return (
    <div className="mb-6 sm:mb-8">
      {/* 数据不一致警告 */}
      {isInconsistent && (
        <div className="mb-4 p-3 bg-amber-900/20 border border-amber-500/30 rounded-lg flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-amber-400 text-sm">
              ⚠️ 统计数据不一致：显示 {stats.total_count}，实际 {actualRecordCount}
            </span>
          </div>
          {onRefreshStats && (
            <button
              onClick={onRefreshStats}
              className="px-3 py-1 bg-amber-600 hover:bg-amber-700 text-white text-xs rounded transition-colors"
            >
              修复统计
            </button>
          )}
        </div>
      )}

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {statsCards.map((card, index) => (
          <div key={index} className="bg-zinc-900/60 backdrop-blur-xl border border-zinc-700/50 rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs sm:text-sm text-zinc-400 truncate">{card.title}</span>
              {card.icon}
            </div>
            <div className={`text-lg sm:text-xl lg:text-2xl font-bold ${card.color} ${isInconsistent && index === 0 ? 'text-amber-400' : ''}`}>
              {card.value.toLocaleString()}
              {isInconsistent && index === 0 && (
                <span className="text-xs text-amber-400 ml-1">!</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
