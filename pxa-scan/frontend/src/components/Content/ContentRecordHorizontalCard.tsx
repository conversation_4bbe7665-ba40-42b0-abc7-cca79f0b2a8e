import Link from 'next/link';
import { DocumentTextIcon, PhotoIcon, VideoCameraIcon, MusicalNoteIcon, BookOpenIcon, DocumentIcon, StarIcon, ShareIcon } from '@heroicons/react/24/outline';
import { ContentRecord } from '@/hooks/useContentDetail';
import { getStatusInfo } from '@/utils/contentManageUtils';
import { ContentStatusIcon } from './ContentStatusIcon';
import { Locale } from '@/lib/i18n';

interface ContentRecordHorizontalCardProps {
  record: ContentRecord;
  locale: Locale;
  formatDate: (dateString: string) => string;
  t: (key: string) => string;
}

// 内容类型图标映射
const getContentTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'article':
      return <DocumentTextIcon className="w-5 h-5" />;
    case 'image':
      return <PhotoIcon className="w-5 h-5" />;
    case 'video':
      return <VideoCameraIcon className="w-5 h-5" />;
    case 'music':
    case 'audio':
      return <MusicalNoteIcon className="w-5 h-5" />;
    case 'novel':
      return <BookOpenIcon className="w-5 h-5" />;
    case 'document':
      return <DocumentIcon className="w-5 h-5" />;
    default:
      return <DocumentTextIcon className="w-5 h-5" />;
  }
};

// 获取原创/分享标签信息
const getContentSourceLabel = (record: ContentRecord) => {
  // 优先使用 is_original 字段
  if (record.is_original === true) {
    return {
      label: '原创',
      icon: <StarIcon className="w-3 h-3" />,
      className: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
    };
  } else if (record.is_original === false) {
    return {
      label: '分享',
      icon: <ShareIcon className="w-3 h-3" />,
      className: 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    };
  }

  // 回退到 content_source 字段
  if (record.content_source === 'original') {
    return {
      label: '原创',
      icon: <StarIcon className="w-3 h-3" />,
      className: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
    };
  } else if (record.content_source === 'shared') {
    return {
      label: '分享',
      icon: <ShareIcon className="w-3 h-3" />,
      className: 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    };
  }

  // 默认返回 null（不显示标签）
  return null;
};

export function ContentRecordHorizontalCard({
  record,
  locale,
  formatDate,
  t
}: ContentRecordHorizontalCardProps) {
  const statusInfo = getStatusInfo(record.status, t);
  const sourceLabel = getContentSourceLabel(record);

  // 检查是否可以点击（使用content_ksuid作为唯一标识）
  const isClickable = record.content_ksuid &&
                     record.status === 'confirmed';

  const cardContent = (
    <div className={`bg-zinc-900/60 backdrop-blur-xl border border-zinc-700/50 rounded-lg sm:rounded-xl transition-all duration-200 ${
      isClickable
        ? 'hover:border-zinc-600/50 hover:bg-zinc-900/80 hover:shadow-lg hover:shadow-zinc-900/20 cursor-pointer group'
        : 'cursor-default opacity-75'
    }`}
    >
      <div className="p-4 sm:p-6">
        <div className="flex items-start gap-4">
          {/* 内容类型图标 */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-zinc-800/50 rounded-lg flex items-center justify-center text-[var(--brand-primary)]">
              {getContentTypeIcon(record.content_type)}
            </div>
          </div>

          {/* 主要内容 */}
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-3">
              <div className="flex-1 min-w-0">
                <h3 className={`text-lg sm:text-xl font-semibold mb-2 line-clamp-1 transition-colors duration-200 ${
                  isClickable ? 'text-white group-hover:text-blue-300' : 'text-zinc-400'
                }`}>
                  {record.title || 'Untitled'}
                  {!isClickable && (
                    <span className="ml-2 text-xs text-amber-400 bg-amber-400/10 px-2 py-1 rounded">
                      上链中...
                    </span>
                  )}
                </h3>
                <p className="text-sm text-zinc-400 line-clamp-2 mb-3">
                  {record.description || 'No description'}
                </p>
              </div>

              {/* 内容类型和状态标签 */}
              <div className="flex items-center gap-2 flex-shrink-0 flex-wrap">
                {/* 原创/分享标签 */}
                {sourceLabel && (
                  <span className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-lg text-xs font-medium border ${sourceLabel.className}`}>
                    {sourceLabel.icon}
                    {sourceLabel.label}
                  </span>
                )}

                {/* 内容类型标签 */}
                <span className="inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-medium bg-blue-600/20 text-blue-300 border border-blue-500/30">
                  影片
                </span>

                {/* 状态标签 */}
                <span className={`inline-flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color} ${statusInfo.borderColor} border`}>
                  <ContentStatusIcon status={statusInfo.iconType} className="w-3 h-3" />
                  {statusInfo.label}
                </span>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <span className="text-xs text-zinc-500 block mb-1">鏈上ID</span>
                <span className="text-sm text-zinc-400 font-mono">
                  {record.on_chain_id && record.content_ksuid ?
                    `${record.on_chain_id}-${record.content_ksuid.slice(0, 8)}...` :
                    'N/A'
                  }
                </span>
              </div>

              <div>
                <span className="text-xs text-zinc-500 block mb-1">{t('fields.publisher')}</span>
                <span className="text-sm text-white font-mono truncate block">
                  {(record as any).publisher_ksuid ?
                    `${(record as any).publisher_ksuid.slice(0, 8)}...${(record as any).publisher_ksuid.slice(-6)}` :
                    (record.publisher || (record as any).creator_address) ?
                      `${(record.publisher || (record as any).creator_address).slice(0, 8)}...${(record.publisher || (record as any).creator_address).slice(-6)}` :
                      'N/A'
                  }
                </span>
              </div>

              {/* 原创作者信息（仅分享内容显示） */}
              {record.is_original === false && record.original_creator && (
                <div>
                  <span className="text-xs text-zinc-500 block mb-1">原創作者</span>
                  <span className="text-sm text-orange-400 truncate block">
                    {record.original_creator}
                  </span>
                </div>
              )}

              <div>
                <span className="text-xs text-zinc-500 block mb-1">{t('fields.createdAt')}</span>
                <span className="text-sm text-white">
                  {record.created_at ? formatDate(record.created_at) : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 根据是否可点击返回不同的组件
  if (isClickable) {
    return (
      <Link href={`/${locale}/content/detail/${record.content_ksuid}`}>
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}
