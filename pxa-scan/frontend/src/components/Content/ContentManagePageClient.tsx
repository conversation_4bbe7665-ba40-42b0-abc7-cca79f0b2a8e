'use client';

import React from 'react';
import { useContentTranslation } from '@/hooks/useContentTranslation';
import { type ContentDictionary } from '@/lib/modularDictionary';
import { Locale } from '@/lib/i18n';

// 导入拆分后的组件和 hooks
import { useContentManage } from '@/hooks/useContentManage';
import { createContentManageUtils } from '@/utils/contentManageUtils';
import { ContentManageHeader } from './ContentManageHeader';
import { ContentStatsCards } from './ContentStatsCards';
import { ContentFilters } from './ContentFilters';
import { ContentTypeTabs } from './ContentTypeTabs';
import { ContentRecordsList } from './ContentRecordsList';
import { ContentPagination } from './ContentPagination';

interface ContentManagePageClientProps {
  locale: Locale;
  dict: ContentDictionary;
}

export default function ContentManagePageClient({ locale, dict }: ContentManagePageClientProps) {
  const { t } = useContentTranslation(dict);

  // 使用拆分后的数据 hook
  const {
    records,
    stats,
    loading,
    currentPage,
    totalPages,
    totalRecords,
    filters,
    handleFilterChange,
    handleSearch,
    handlePageChange,
    forceRecalculateStats
  } = useContentManage({ recordsPerPage: 20 });

  // 创建工具函数
  const { formatDate } = createContentManageUtils(locale);

  // 自动检测统计数据不一致并修复
  React.useEffect(() => {
    if (records.length > 0 && stats && stats.total_count !== records.length) {
      console.log(`🔧 Auto-fixing stats inconsistency: stats=${stats.total_count}, records=${records.length}`);
      // 延迟一点时间再修复，确保数据加载完成
      const timer = setTimeout(() => {
        forceRecalculateStats();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [records.length, stats?.total_count, forceRecalculateStats]);

  return (
    <section className="relative py-8 sm:py-12 lg:py-20 overflow-hidden">
      <div className="max-w-[1600px] mx-auto px-4 sm:px-6">
        {/* 页面头部 */}
        <ContentManageHeader t={t} />

        {/* 统计卡片 */}
        <ContentStatsCards
          stats={stats}
          t={t}
          onRefreshStats={forceRecalculateStats}
          actualRecordCount={records.length}
        />

        {/* 搜索和过滤器 */}
        <ContentFilters
          searchTerm={filters.search}
          contentTypeFilter={filters.content_type}
          statusFilter={filters.status}
          onSearchChange={handleSearch}
          onContentTypeChange={(value) => handleFilterChange('content_type', value)}
          onStatusChange={(value) => handleFilterChange('status', value)}
          t={t}
        />

        {/* 调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-3 bg-zinc-800/50 rounded-lg text-xs text-zinc-400">
            <div className="flex items-center justify-between">
              <div>
                <div>🔍 当前过滤器: {JSON.stringify(filters)}</div>
                <div>📊 显示记录数量: {records.length}</div>
                <div>📋 记录类型分布: {JSON.stringify(
                  records.reduce((acc, r) => {
                    const type = r.content_type || 'unknown';
                    acc[type] = (acc[type] || 0) + 1;
                    return acc;
                  }, {} as Record<string, number>)
                )}</div>
              </div>
              <button
                onClick={() => {
                  console.log('🔍 Debug - Current records:', records);
                  console.log('🔍 Debug - Current filters:', filters);
                  console.log('🔍 Debug - Current stats:', stats);
                }}
                className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded"
              >
                打印调试信息
              </button>
            </div>
          </div>
        )}

        {/* 内容类型标签页 */}
        <ContentTypeTabs
          activeType={filters.content_type || 'all'}
          onTypeChange={(value) => handleFilterChange('content_type', value === 'all' ? '' : value)}
          stats={stats}
          t={t}
        />

        {/* 内容列表 */}
        <ContentRecordsList
          records={records}
          loading={loading}
          locale={locale}
          formatDate={formatDate}
          t={t}
        />

        {/* 分页 */}
        <ContentPagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={20}
          onPageChange={handlePageChange}
          t={t}
        />
      </div>
    </section>
  );
}
