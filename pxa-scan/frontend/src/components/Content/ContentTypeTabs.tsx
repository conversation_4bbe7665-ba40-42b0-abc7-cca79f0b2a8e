import { ContentStats } from '@/hooks/useContentDetail';

interface ContentTypeTabsProps {
  activeType: string;
  onTypeChange: (type: string) => void;
  stats: ContentStats | null;
  t: (key: string) => string;
}

export function ContentTypeTabs({ activeType, onTypeChange, stats, t }: ContentTypeTabsProps) {
  // 计算总数
  const totalCount = stats?.total_count || 0;

  // 获取各类型的数量，如果没有统计数据则显示0
  const getTypeCount = (type: string) => {
    if (!stats?.type_stats) return 0;
    return stats.type_stats[type] || 0;
  };

  const tabs = [
    { id: 'all', label: t('filters.allTypes') || '全部類型', count: totalCount },
    { id: 'article', label: t('contentTypes.article') || '文章', count: getTypeCount('article') },
    { id: 'image', label: t('contentTypes.image') || '圖片', count: getTypeCount('image') },
    { id: 'video', label: t('contentTypes.video') || '影片', count: getTypeCount('video') },
    { id: 'music', label: t('contentTypes.music') || '音樂', count: getTypeCount('music') + getTypeCount('audio') }, // 合并音乐和音频
    { id: 'novel', label: t('contentTypes.novel') || '小說', count: getTypeCount('novel') },
    { id: 'document', label: t('contentTypes.document') || '文件', count: getTypeCount('document') },
    { id: 'other', label: t('contentTypes.other') || '其他', count: getTypeCount('other') + getTypeCount('unknown') }
  ];

  // 添加调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 ContentTypeTabs - Stats:', stats);
    console.log('📊 ContentTypeTabs - Type stats:', stats?.type_stats);
    console.log('📊 ContentTypeTabs - Active type:', activeType);
  }

  return (
    <div className="mb-6 sm:mb-8">
      <div className="border-b border-zinc-700/50">
        <nav className="flex space-x-6 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTypeChange(tab.id)}
              className={`flex items-center gap-2 py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-all duration-200 ${
                activeType === tab.id
                  ? 'border-blue-500 text-blue-400'
                  : 'border-transparent text-zinc-400 hover:text-zinc-300 hover:border-zinc-600'
              }`}
            >
              <span>{tab.label}</span>
              <span className={`px-2 py-0.5 rounded-full text-xs ${
                activeType === tab.id
                  ? 'bg-blue-500/20 text-blue-400'
                  : 'bg-zinc-700/50 text-zinc-500'
              }`}>
                ({tab.count})
              </span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}
