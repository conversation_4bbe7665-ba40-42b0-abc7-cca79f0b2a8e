import { useState, useEffect } from 'react';

export interface Collaborator {
  user_ksuid: string;
  role_name: string;
  username?: string;
  nickname?: string;
}

export interface AuditorLog {
  user_ksuid: string;
  vote_type: string;
  vote_weight: number;
  reason: string;
  created_at: string;
}

export interface AuditInfo {
  status: string;
  votes: number;
  permit_num: number;
  reject_num: number;
  auditor_logs: AuditorLog[];
}

export interface IPFSContent {
  id: string;
  on_chain_id: string;
  content_ksuid: string; // 内容KSUID
  title: string;
  description: string;
  content_type: string;
  publisher_ksuid: string;
  publisher?: string; // 区块链地址
  creator_address?: string; // API返回的创建者地址字段
  creator_ksuid?: string; // 创建者KSUID
  auditor_ksuids?: string[]; // 多个审核员KSUID
  ipfs_hash: string;
  metadata_ipfs: string;
  transaction_hash?: string;
  block_number?: number;
  transfer_amount?: string; // 转账金额
  transfer_token?: string; // 转账代币类型
  fee_paid?: string; // API返回的费用字段
  original_tags?: string; // 原始标签
  collaborators?: Collaborator[]; // 合作者信息
  audit_info?: AuditInfo; // 审核信息
  category?: Record<string, unknown>; // 分类信息
  status: 'pending' | 'confirmed' | 'failed';
  license_number: string;
  reviewers?: string[] | null;
  attributes?: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
  // API返回的额外字段
  video_id?: string;
  cover?: string;
  cover_url?: string;
  play_url?: string;
  preview_url?: string;
  duration?: number;
  language?: string;
  orientation?: string;
  rating?: string;
  like_count?: number;
  comment_count?: number;
  favorite_count?: number;
  all_approved?: boolean;
  // 原创/分享相关字段
  content_source?: 'original' | 'shared';
  is_original?: boolean;
  sharing_type?: 'original_creation' | 'content_sharing';
  source_platform?: string | null;
  source_url?: string | null;
  original_creator?: string;
  copyright_status?: 'original' | 'shared_with_permission';
}

// 内容记录类型（与 IPFSContent 相同，但为了清晰性保留）
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ContentRecord extends IPFSContent {}

// 内容统计类型
export interface ContentStats {
  total_count: number;
  confirmed_count: number;
  pending_count: number;
  failed_count: number;
  type_stats: Record<string, number>;
  ipfs_stats?: Record<string, any>;
}

interface UseContentDetailProps {
  onChainId: string;
}

interface UseContentDetailReturn {
  record: IPFSContent | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useContentDetail({ onChainId }: UseContentDetailProps): UseContentDetailReturn {
  const [record, setRecord] = useState<IPFSContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadRecord = async () => {
    try {
      setLoading(true);
      setError(null);

      // 检查onChainId是否有效
      if (!onChainId || onChainId === '<nil>' || onChainId === 'null' || onChainId === 'undefined') {
        console.log('无效的onChainId:', onChainId);
        throw new Error('内容ID无效，可能内容还在上链处理中，请稍后再试');
      }

      // 首先尝试从链上内容接口获取
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const onChainResponse = await fetch(`${API_BASE_URL}/api/v1/content/on-chain/${onChainId}`);
      console.log('链上内容接口响应状态:', onChainResponse.status);

      if (onChainResponse.ok) {
        const onChainData = await onChainResponse.json();
        console.log('链上内容接口数据:', onChainData);

        if (onChainData.success && onChainData.data) {
          console.log('成功获取链上内容数据');
          setRecord(onChainData.data);
          return;
        } else {
          console.log('链上内容数据格式不正确:', { success: onChainData.success, hasData: !!onChainData.data });
        }
      } else {
        console.log('链上内容接口请求失败');
      }

      // 如果链上内容接口失败，回退到IPFS接口
      try {
        const ipfsResponse = await fetch(`${API_BASE_URL}/api/v1/content/ipfs/${onChainId}`);
        if (ipfsResponse.ok) {
          const ipfsData = await ipfsResponse.json();
          if (ipfsData.success && ipfsData.data) {
            setRecord(ipfsData.data);
            return;
          }
        }
      } catch (ipfsError) {
        console.log('IPFS接口也失败了:', ipfsError);
      }

      // 如果两个接口都失败，抛出错误
      throw new Error('无法加载内容详情，可能内容还在上链处理中，请稍后再试');
    } catch (err) {
      console.error('Error loading content:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (onChainId) {
      loadRecord();
    }
  }, [onChainId]);

  return {
    record,
    loading,
    error,
    refetch: loadRecord
  };
}
