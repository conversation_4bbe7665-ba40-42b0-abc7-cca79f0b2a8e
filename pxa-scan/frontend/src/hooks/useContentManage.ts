import { useState, useEffect } from 'react';
import { ContentRecord, ContentStats } from './useContentDetail';

interface ContentFilters {
  content_type: string;
  status: string;
  search: string;
}

interface UseContentManageProps {
  recordsPerPage?: number;
}

interface UseContentManageReturn {
  // 数据状态
  records: ContentRecord[];
  stats: ContentStats | null;
  loading: boolean;
  
  // 分页状态
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  
  // 过滤器状态
  filters: ContentFilters;
  
  // 操作函数
  setCurrentPage: (page: number) => void;
  handleFilterChange: (key: string, value: string) => void;
  handleSearch: (searchTerm: string) => void;
  handlePageChange: (page: number) => void;
  refetchRecords: () => Promise<void>;
  refetchStats: () => Promise<void>;
}

export function useContentManage({ 
  recordsPerPage = 20 
}: UseContentManageProps = {}): UseContentManageReturn {
  const [records, setRecords] = useState<ContentRecord[]>([]);
  const [stats, setStats] = useState<ContentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [filters, setFilters] = useState<ContentFilters>({
    content_type: '',
    status: '',
    search: ''
  });

  // 获取内容记录
  const fetchRecords = async () => {
    try {
      setLoading(true);
      
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: recordsPerPage.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([, value]) => value))
      });

      // 尝试多个内容接口
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const endpoints = [
        `${API_BASE_URL}/api/v1/content/on-chain?${queryParams}`,
        `${API_BASE_URL}/api/v1/content/ipfs?${queryParams}`
      ];

      let recordsData = [];
      let totalCount = 0;
      let success = false;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint);

          if (!response.ok) {
            console.log(`Endpoint ${endpoint} returned status: ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`Endpoint ${endpoint} response:`, data);

          if (data.success) {
            // 处理链上内容数据
            if (endpoint.includes('on-chain')) {
              recordsData = Array.isArray(data.data?.contents) ? data.data.contents : [];
              totalCount = data.data?.pagination?.total || recordsData.length || 0;
              console.log('Successfully fetched on-chain content:', recordsData.length, 'records');
            }
            // 处理IPFS内容数据
            else if (endpoint.includes('ipfs')) {
              recordsData = Array.isArray(data.data?.contents) ? data.data.contents :
                           (data.data?.contents === null ? [] : []);
              totalCount = data.data?.pagination?.total || recordsData.length || 0;
              console.log('Successfully fetched IPFS content:', recordsData.length, 'records');
            }
            success = true;
            break;
          } else {
            console.log(`Endpoint ${endpoint} returned unsuccessful response:`, data);
          }
        } catch (err) {
          console.log(`Content endpoint ${endpoint} failed:`, err);
          continue;
        }
      }

      if (success) {
        setRecords(recordsData);
        setTotalRecords(totalCount);
        setTotalPages(Math.ceil(totalCount / recordsPerPage));
      } else {
        console.error('All content endpoints failed');
        setRecords([]);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error('Error fetching content records:', error);
      setRecords([]);
      setTotalRecords(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据 - 基于实际获取到的记录进行统计
  const fetchStats = async () => {
    try {
      // 首先尝试从API获取统计数据
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const endpoints = [
        `${API_BASE_URL}/api/v1/content/on-chain/stats`,
        `${API_BASE_URL}/api/v1/content/ipfs/stats`,
        `${API_BASE_URL}/api/v1/content/stats`,
        `${API_BASE_URL}/api/v1/ipfs/stats`
      ];

      let statsData = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying stats endpoint: ${endpoint}`);
          const response = await fetch(endpoint);
          if (response.ok) {
            const data = await response.json();
            console.log(`Stats endpoint ${endpoint} response:`, data);
            if (data.success) {
              statsData = data.data || {};
              console.log('Successfully fetched stats from API:', statsData);
              break;
            }
          } else {
            console.log(`Stats endpoint ${endpoint} returned status: ${response.status}`);
          }
        } catch (err) {
          console.log(`Stats endpoint ${endpoint} failed:`, err);
          continue;
        }
      }

      // 如果API统计失败，基于当前获取到的所有记录进行统计
      if (!statsData) {
        console.log('API stats failed, calculating stats from all records...');
        statsData = await calculateStatsFromAllRecords();
      }

      setStats(statsData);
    } catch (error) {
      console.error('Error fetching content stats:', error);
      // 最后的备用方案：基于当前记录计算统计
      const fallbackStats = await calculateStatsFromAllRecords();
      setStats(fallbackStats);
    }
  };

  // 基于所有记录计算统计数据
  const calculateStatsFromAllRecords = async (): Promise<ContentStats> => {
    try {
      console.log('Calculating stats from all available records...');

      // 获取所有记录（不分页）
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const endpoints = [
        `${API_BASE_URL}/api/v1/content/on-chain?limit=1000`,
        `${API_BASE_URL}/api/v1/content/ipfs?limit=1000`
      ];

      let allRecords: ContentRecord[] = [];

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data?.contents) {
              const records = Array.isArray(data.data.contents) ? data.data.contents : [];
              allRecords = [...allRecords, ...records];
              console.log(`Got ${records.length} records from ${endpoint}`);
            }
          }
        } catch (err) {
          console.log(`Failed to fetch records from ${endpoint}:`, err);
        }
      }

      // 去重（基于content_ksuid）
      const uniqueRecords = allRecords.reduce((acc, record) => {
        const key = record.content_ksuid || record.on_chain_id || `${record.title}_${record.created_at}`;
        if (!acc.has(key)) {
          acc.set(key, record);
        }
        return acc;
      }, new Map<string, ContentRecord>());

      const records = Array.from(uniqueRecords.values());
      console.log(`Total unique records for stats calculation: ${records.length}`);

      // 计算统计数据
      const stats = {
        total_count: records.length,
        confirmed_count: records.filter(r => r.status === 'confirmed').length,
        pending_count: records.filter(r => r.status === 'pending').length,
        failed_count: records.filter(r => r.status === 'failed').length,
        type_stats: records.reduce((acc, record) => {
          const type = record.content_type || 'unknown';
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      };

      console.log('Calculated stats from records:', stats);
      return stats;
    } catch (error) {
      console.error('Error calculating stats from records:', error);
      return {
        total_count: 0,
        confirmed_count: 0,
        pending_count: 0,
        failed_count: 0,
        type_stats: {}
      };
    }
  };

  // 处理过滤器变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // 重置到第一页
  };

  // 处理搜索
  const handleSearch = (searchTerm: string) => {
    handleFilterChange('search', searchTerm);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  useEffect(() => {
    fetchRecords();
  }, [currentPage, filters, recordsPerPage]);

  useEffect(() => {
    fetchStats();
  }, []);

  // 当记录发生变化时，重新计算统计数据（如果API统计不可用）
  useEffect(() => {
    if (records.length > 0 && stats && stats.total_count === 0) {
      console.log('Records loaded but stats show 0, recalculating stats...');
      fetchStats();
    }
  }, [records]);

  return {
    // 数据状态
    records,
    stats,
    loading,
    
    // 分页状态
    currentPage,
    totalPages,
    totalRecords,
    
    // 过滤器状态
    filters,
    
    // 操作函数
    setCurrentPage,
    handleFilterChange,
    handleSearch,
    handlePageChange,
    refetchRecords: fetchRecords,
    refetchStats: fetchStats
  };
}
