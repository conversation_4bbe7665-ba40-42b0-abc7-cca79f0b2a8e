'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  // PhotoIcon,
  // MusicalNoteIcon,
  // VideoCameraIcon,
  // BookOpenIcon
} from '@heroicons/react/24/outline';

// 内容记录类型
interface ContentRecord {
  id: string;
  on_chain_id: string;
  title: string;
  description: string;
  content_type: string;
  publisher_ksuid: string;
  publisher: string;
  ipfs_hash: string;
  metadata_ipfs: string;
  transaction_hash?: string;
  block_number?: number;
  status: 'pending' | 'confirmed' | 'failed';
  license_number: string;
  reviewers: string[];
  attributes: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

// 内容统计类型
interface ContentStats {
  total_count: number;
  confirmed_count: number;
  pending_count: number;
  failed_count: number;
  type_stats: Record<string, number>;
}

export default function ContentManagePage() {
  const [records, setRecords] = useState<ContentRecord[]>([]);
  const [stats, setStats] = useState<ContentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    content_type: '',
    status: '',
  });

  // 获取内容类型选项
  const getContentTypeOptions = () => [
    { value: 'article', label: '文章' },
    { value: 'image', label: '图片' },
    { value: 'music', label: '音乐' },
    { value: 'video', label: '视频' },
    { value: 'novel', label: '小说' },
    { value: 'document', label: '文档' }
  ];

  // 获取状态选项
  const getStatusOptions = () => [
    { value: 'confirmed', label: '已确认' },
    { value: 'pending', label: '待确认' },
    { value: 'failed', label: '失败' }
  ];

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
      const [recordsData, statsData] = await Promise.all([
        fetch(`${API_BASE_URL}/api/v1/content/ipfs?page=${currentPage}&limit=20${filters.content_type ? `&content_type=${filters.content_type}` : ''}${filters.status ? `&status=${filters.status}` : ''}`).then(res => res.json()),
        fetch(`${API_BASE_URL}/api/v1/content/ipfs/stats`).then(res => res.json())
      ]);

      if (recordsData.success) {
        setRecords(recordsData.data.contents || []);
        setTotalPages(recordsData.data.pagination?.total_pages || 1);
      }

      if (statsData.success) {
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Failed to load content data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [currentPage, filters]);

  // 处理筛选变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // 重置到第一页
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取内容类型标签
  const getContentTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      'article': '文章',
      'image': '图片',
      'music': '音乐',
      'video': '视频',
      'novel': '小说',
      'document': '文档'
    };
    return types[type] || type;
  };

  // 获取状态信息
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { label: '已确认', color: 'green' };
      case 'pending':
        return { label: '待确认', color: 'yellow' };
      case 'failed':
        return { label: '失败', color: 'red' };
      default:
        return { label: '未知', color: 'gray' };
    }
  };

  return (
    <div className="relative overflow-hidden bg-transparent">
      {/* 主容器 - 参考BSC页面的布局 */}
      <div className="mx-auto max-w-[1366px] px-2 sm:px-4 lg:px-6 xl:px-8 py-8">

        {/* 页面头部 */}
        <div className="m-2 md:m-3 lg:m-4">
          <div className="mb-16 text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl mb-6">
              <span className="hero-gradient-text">
                IPFS 内容上链记录
              </span>
            </h1>
            <p className="text-xl text-white mb-6">查看所有IPFS内容的上链记录和状态</p>
          </div>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="m-2 md:m-3 lg:m-4">
            <div className="py-4">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 lg:gap-6">
                <div className="bg-zinc-900/50 border border-zinc-800 rounded-lg p-3 md:p-4 lg:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-blue-100 dark:bg-blue-950/20 rounded-lg flex items-center justify-center mx-auto sm:mx-0 mb-2 sm:mb-0">
                      <DocumentTextIcon className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="sm:ml-3 md:ml-4 flex-1 text-center sm:text-left">
                      <p className="text-xs md:text-sm text-zinc-400 mb-1">总内容数</p>
                      <p className="text-sm md:text-base lg:text-lg font-bold text-white">{stats.total_count}</p>
                      <div className="flex items-center justify-center sm:justify-start text-xs mt-1">
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full mr-1 bg-blue-400 animate-pulse"></div>
                        <span className="text-blue-400">实时</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-zinc-900/50 border border-zinc-800 rounded-lg p-3 md:p-4 lg:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-emerald-100 dark:bg-emerald-950/20 rounded-lg flex items-center justify-center mx-auto sm:mx-0 mb-2 sm:mb-0">
                      <CheckCircleIcon className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <div className="sm:ml-3 md:ml-4 flex-1 text-center sm:text-left">
                      <p className="text-xs md:text-sm text-zinc-400 mb-1">已确认内容</p>
                      <p className="text-sm md:text-base lg:text-lg font-bold text-white">{stats.confirmed_count}</p>
                      <div className="flex items-center justify-center sm:justify-start text-xs mt-1">
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full mr-1 bg-emerald-400 animate-pulse"></div>
                        <span className="text-emerald-400">实时</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-zinc-900/50 border border-zinc-800 rounded-lg p-3 md:p-4 lg:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-yellow-100 dark:bg-yellow-950/20 rounded-lg flex items-center justify-center mx-auto sm:mx-0 mb-2 sm:mb-0">
                      <ClockIcon className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="sm:ml-3 md:ml-4 flex-1 text-center sm:text-left">
                      <p className="text-xs md:text-sm text-zinc-400 mb-1">待确认内容</p>
                      <p className="text-sm md:text-base lg:text-lg font-bold text-white">{stats.pending_count}</p>
                      <div className="flex items-center justify-center sm:justify-start text-xs mt-1">
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full mr-1 bg-yellow-400 animate-pulse"></div>
                        <span className="text-yellow-400">实时</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-zinc-900/50 border border-zinc-800 rounded-lg p-3 md:p-4 lg:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center">
                    <div className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-red-100 dark:bg-red-950/20 rounded-lg flex items-center justify-center mx-auto sm:mx-0 mb-2 sm:mb-0">
                      <XCircleIcon className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="sm:ml-3 md:ml-4 flex-1 text-center sm:text-left">
                      <p className="text-xs md:text-sm text-zinc-400 mb-1">失败内容</p>
                      <p className="text-sm md:text-base lg:text-lg font-bold text-white">{stats.failed_count}</p>
                      <div className="flex items-center justify-center sm:justify-start text-xs mt-1">
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full mr-1 bg-red-400 animate-pulse"></div>
                        <span className="text-red-400">实时</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 筛选器 */}
        <div className="m-2 md:m-3 lg:m-4">
          <div className="py-4">
            <h3 className="text-sm md:text-base lg:text-lg font-semibold text-white mb-3 md:mb-4">筛选条件</h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 md:gap-4">
              <div>
                <label className="block text-xs md:text-sm font-medium text-zinc-300 mb-2">内容类型</label>
                <select
                  value={filters.content_type}
                  onChange={(e) => handleFilterChange('content_type', e.target.value)}
                  className="w-full px-2 md:px-3 py-1.5 md:py-2 text-xs md:text-sm bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部类型</option>
                  {getContentTypeOptions().map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs md:text-sm font-medium text-zinc-300 mb-2">状态</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-2 md:px-3 py-1.5 md:py-2 text-xs md:text-sm bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部状态</option>
                  {getStatusOptions().map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => {
                    setFilters({ content_type: '', status: '' });
                    setCurrentPage(1);
                  }}
                  className="w-full lg:w-auto px-3 md:px-4 py-1.5 md:py-2 text-xs md:text-sm text-zinc-300 hover:text-white border border-zinc-700 rounded-md hover:bg-zinc-800 transition-colors"
                >
                  清除筛选
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 内容列表 */}
        <div className="m-2 md:m-3 lg:m-4">
          <div className="py-4 overflow-hidden">
            {loading ? (
              <div className="p-4 md:p-8 text-center">
                <div className="text-zinc-400 text-xs md:text-sm">加载中...</div>
              </div>
            ) : records.length === 0 ? (
              <div className="p-4 md:p-8 text-center">
                <div className="text-zinc-400 text-xs md:text-sm">暂无内容记录</div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b border-zinc-800 bg-zinc-800/50">
                    <tr>
                      {/* 移动端只显示：内容信息、状态、操作 */}
                      <th className="md:hidden w-1/2 px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        内容信息
                      </th>
                      <th className="md:hidden w-1/4 px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="md:hidden w-1/4 px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        操作
                      </th>

                      {/* PC端显示所有列 */}
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        内容信息
                      </th>
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        类型
                      </th>
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        IPFS
                      </th>
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        上链时间
                      </th>
                      <th className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-zinc-800">
                    {records.map((record) => {
                      const statusInfo = getStatusInfo(record.status);
                      return (
                        <tr key={record.id} className="hover:bg-zinc-800/30">
                          {/* 移动端布局：内容信息、状态、操作 */}
                          <td className="md:hidden w-1/2 px-3 py-3">
                            <div>
                              <div className="font-medium text-white text-xs">{record.title}</div>
                              <div className="text-xs text-zinc-500 mt-1">
                                ID: {record.on_chain_id}
                              </div>
                              <div className="flex items-center gap-2 mt-2">
                                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                                  {getContentTypeLabel(record.content_type)}
                                </span>
                                <span className="text-xs text-white">
                                  {record.publisher}
                                </span>
                              </div>
                            </div>
                          </td>
                          <td className="md:hidden w-1/4 px-3 py-3">
                            <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${
                              statusInfo.color === 'green' ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30' :
                              statusInfo.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30' :
                              statusInfo.color === 'red' ? 'bg-red-500/20 text-red-300 border-red-500/30' :
                              'bg-zinc-500/20 text-zinc-300 border-zinc-500/30'
                            }`}>
                              {statusInfo.label}
                            </span>
                          </td>
                          <td className="md:hidden w-1/4 px-3 py-3">
                            {record.on_chain_id &&
                             record.on_chain_id !== '<nil>' &&
                             record.on_chain_id !== 'null' &&
                             record.on_chain_id !== 'undefined' &&
                             record.status === 'confirmed' ? (
                              <Link
                                href={`/zh/content/detail/${record.on_chain_id}_${(record as any).content_ksuid || record.on_chain_id}`}
                                className="text-blue-400 hover:text-blue-300 text-xs"
                              >
                                详情
                              </Link>
                            ) : (
                              <span className="text-amber-400 text-xs">上链中...</span>
                            )}
                          </td>

                          {/* PC端布局：所有列 */}
                          <td className="hidden md:table-cell px-6 py-4">
                            <div>
                              <div className="font-medium text-white">{record.title}</div>
                              <div className="text-xs text-zinc-500 mt-1">
                                ID: {record.on_chain_id}
                              </div>
                              <div className="text-xs text-zinc-400 mt-1">
                                发布者: {record.publisher}
                              </div>
                            </div>
                          </td>
                          <td className="hidden md:table-cell px-6 py-4">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                              {getContentTypeLabel(record.content_type)}
                            </span>
                          </td>
                          <td className="hidden md:table-cell px-6 py-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                              statusInfo.color === 'green' ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30' :
                              statusInfo.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30' :
                              statusInfo.color === 'red' ? 'bg-red-500/20 text-red-300 border-red-500/30' :
                              'bg-zinc-500/20 text-zinc-300 border-zinc-500/30'
                            }`}>
                              {statusInfo.label}
                            </span>
                          </td>
                          <td className="hidden md:table-cell px-6 py-4">
                            <a
                              href={`https://gateway.pinata.cloud/ipfs/${record.ipfs_hash}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-400 hover:text-blue-300 text-sm font-mono"
                            >
                              {record.ipfs_hash.slice(0, 8)}...{record.ipfs_hash.slice(-8)}
                            </a>
                          </td>
                          <td className="hidden md:table-cell px-6 py-4 text-sm text-zinc-400">
                            {formatDate(record.created_at)}
                          </td>
                          <td className="hidden md:table-cell px-6 py-4">
                            {record.on_chain_id &&
                             record.on_chain_id !== '<nil>' &&
                             record.on_chain_id !== 'null' &&
                             record.on_chain_id !== 'undefined' &&
                             record.status === 'confirmed' ? (
                              <Link
                                href={`/zh/content/detail/${record.on_chain_id}_${(record as any).content_ksuid || record.on_chain_id}`}
                                className="text-blue-400 hover:text-blue-300 text-sm"
                              >
                                查看详情
                              </Link>
                            ) : (
                              <span className="text-amber-400 text-sm">上链中...</span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="px-3 md:px-6 py-3 md:py-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                  <div className="text-xs md:text-sm text-zinc-400 text-center sm:text-left">
                    第 {currentPage} 页，共 {totalPages} 页
                  </div>
                  <div className="flex gap-2 justify-center sm:justify-end">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-2 md:px-3 py-1 text-xs md:text-sm border border-zinc-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-zinc-800 text-zinc-300"
                    >
                      上一页
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-2 md:px-3 py-1 text-xs md:text-sm border border-zinc-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-zinc-800 text-zinc-300"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
