package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"pxa-scan/internal/models"
	"pxa-scan/pkg/logger"
)

// AsyncProcessor 异步处理器
type AsyncProcessor struct {
	db            *mongo.Database
	asyncService  *AsyncUploadService
	ethClient     *ethclient.Client
	contractAddr  common.Address
	privateKey    string
}

// NewAsyncProcessor 创建异步处理器
func NewAsyncProcessor(db *mongo.Database, ethClient *ethclient.Client, contractAddr common.Address, privateKey string) *AsyncProcessor {
	return &AsyncProcessor{
		db:           db,
		asyncService: NewAsyncUploadService(db),
		ethClient:    ethClient,
		contractAddr: contractAddr,
		privateKey:   privateKey,
	}
}

// ProcessTask 处理单个任务
func (p *AsyncProcessor) ProcessTask(ctx context.Context, task *models.AsyncUploadTask) error {
	log.Printf("=== ProcessTask: Starting to process async task ID=%s, content_ksuid=%s, title=%s ===",
		task.ID.Hex(), task.ContentKSUID, task.Title)

	// 标记任务为处理中
	log.Printf("=== ProcessTask: Marking task as processing ID=%s ===", task.ID.Hex())
	if err := p.asyncService.MarkTaskAsProcessing(ctx, task.ID); err != nil {
		log.Printf("=== ProcessTask: Failed to mark task as processing ID=%s, error=%v ===", task.ID.Hex(), err)
		return fmt.Errorf("failed to mark task as processing: %w", err)
	}
	log.Printf("=== ProcessTask: Task marked as processing successfully ID=%s ===", task.ID.Hex())

	// 步骤1: 解析文件数据 (假设FileData是一个JSON字符串)
	log.Printf("=== ProcessTask: Step 1 - Parsing file data ID=%s ===", task.ID.Hex())
	if err := p.asyncService.UpdateTaskFunction(ctx, task.ID, "parsing_file_data"); err != nil {
		log.Printf("=== ProcessTask: Failed to update task function ID=%s, error=%v ===", task.ID.Hex(), err)
	}

	// 首先尝试Base64解码FileData
	previewLen := 100
	if len(task.FileData) < previewLen {
		previewLen = len(task.FileData)
	}
	log.Printf("=== ProcessTask: FileData length=%d, first %d chars=%s ID=%s ===",
		len(task.FileData), previewLen, task.FileData[:previewLen], task.ID.Hex())

	var decodedData []byte
	var err error

	// 尝试Base64解码
	if decodedData, err = base64.StdEncoding.DecodeString(task.FileData); err != nil {
		log.Printf("=== ProcessTask: FileData is not Base64, treating as plain text ID=%s ===", task.ID.Hex())
		decodedData = []byte(task.FileData)
	} else {
		log.Printf("=== ProcessTask: FileData decoded from Base64, length=%d ID=%s ===", len(decodedData), task.ID.Hex())
	}

	var fileInfo struct {
		PlayURL string `json:"play_url"`
	}

	log.Printf("=== ProcessTask: Parsing decoded data as JSON, length=%d ID=%s ===", len(decodedData), task.ID.Hex())
	if err := json.Unmarshal(decodedData, &fileInfo); err != nil {
		log.Printf("=== ProcessTask: Failed to parse file data as JSON ID=%s, error=%v ===", task.ID.Hex(), err)
		previewLen := 200
		if len(decodedData) < previewLen {
			previewLen = len(decodedData)
		}
		log.Printf("=== ProcessTask: Decoded data preview: %s ID=%s ===", string(decodedData[:previewLen]), task.ID.Hex())
		p.asyncService.MarkTaskAsFailed(ctx, task.ID, fmt.Sprintf("Failed to parse file data: %v", err))
		return fmt.Errorf("failed to parse file data: %w", err)
	}
	log.Printf("=== ProcessTask: File data parsed successfully ID=%s, play_url=%s ===", task.ID.Hex(), fileInfo.PlayURL)

	// 这里的 fileData 应该是从 play_url 下载的文件内容，
	// 但为了简化，我们暂时假设 fileData 就是 task.FileData 本身。
	// 在实际应用中，您需要在这里添加从 URL 下载文件的逻辑。
	fileData := []byte(task.FileData)

	// 步骤2: 上传文件到IPFS
	log.Printf("=== ProcessTask: Step 2 - Uploading to IPFS ID=%s ===", task.ID.Hex())
	if err := p.asyncService.UpdateTaskFunction(ctx, task.ID, "uploading_to_ipfs"); err != nil {
		log.Printf("=== ProcessTask: Failed to update task function ID=%s, error=%v ===", task.ID.Hex(), err)
	}

	log.Printf("=== ProcessTask: Calling uploadToIPFS with fileData length=%d, fileName=%s ID=%s ===",
		len(fileData), task.FileName, task.ID.Hex())
	ipfsHash, err := p.uploadToIPFS(fileData, task.FileName, task.Metadata)
	if err != nil {
		log.Printf("=== ProcessTask: Failed to upload to IPFS ID=%s, error=%v ===", task.ID.Hex(), err)
		p.asyncService.MarkTaskAsFailed(ctx, task.ID, fmt.Sprintf("Failed to upload to IPFS: %v", err))
		return fmt.Errorf("failed to upload to IPFS: %w", err)
	}

	log.Printf("=== ProcessTask: IPFS upload successful ID=%s, ipfs_hash=%s ===", task.ID.Hex(), ipfsHash)

	// 步骤3: 创建并上传元数据
	if err := p.asyncService.UpdateTaskFunction(ctx, task.ID, "uploading_metadata"); err != nil {
		logger.Error("Failed to update task function", map[string]interface{}{"error": err.Error()})
	}

	metadata := map[string]interface{}{
		"title":       task.Title,
		"description": task.Description,
		"type":        task.ContentType,
		"ipfs_hash":   ipfsHash,
		"timestamp":   time.Now().Unix(),
		"creator":     "0x4efd9C31B65f4799310291954Fed29cEc7E69A98",
	}
	if task.Metadata != nil {
		for k, v := range task.Metadata {
			metadata[k] = v
		}
	}

	metadataJSON, _ := json.Marshal(metadata)
	metadataURI, err := p.uploadToIPFS(metadataJSON, "metadata.json", nil)
	if err != nil {
		p.asyncService.MarkTaskAsFailed(ctx, task.ID, fmt.Sprintf("Failed to upload metadata: %v", err))
		return fmt.Errorf("failed to upload metadata: %w", err)
	}

	// 步骤4: 上链到区块链
	if err := p.asyncService.UpdateTaskFunction(ctx, task.ID, "uploading_to_blockchain"); err != nil {
		logger.Error("Failed to update task function", map[string]interface{}{"error": err.Error()})
	}

	result, err := p.directBlockchainUpload(task.ContentKSUID, task.Title, task.ContentType, ipfsHash, metadataURI)
	if err != nil {
		p.asyncService.MarkTaskAsFailed(ctx, task.ID, fmt.Sprintf("Failed to upload to blockchain: %v", err))
		return fmt.Errorf("failed to upload to blockchain: %w", err)
	}

	// 步骤5: 保存到数据库
	if err := p.asyncService.UpdateTaskFunction(ctx, task.ID, "saving_to_database"); err != nil {
		logger.Error("Failed to update task function", map[string]interface{}{"error": err.Error()})
	}

	// 保存完整的链上内容记录
	if err := p.saveToDatabase(ctx, task, result, ipfsHash, metadataURI); err != nil {
		logger.Error("Failed to save to database", map[string]interface{}{
			"error": err.Error(),
		})
		// 数据库保存失败不应该标记整个任务失败，因为区块链上链已经成功
	}

	// 标记任务完成
	taskResult := map[string]interface{}{
		"ipfs_hash":        ipfsHash,
		"metadata_uri":     metadataURI,
		"transaction_hash": result.TransactionHash,
		"block_number":     result.BlockNumber,
		"content_id":       result.ContentID,
		"gas_used":         result.GasUsed,
		"fee_paid":         result.FeePaid,
	}

	if err := p.asyncService.MarkTaskAsCompleted(ctx, task.ID, taskResult); err != nil {
		logger.Error("Failed to mark task as completed", map[string]interface{}{
			"error": err.Error(),
		})
	}

	logger.Info("Async task completed successfully", map[string]interface{}{
		"task_id":          task.ID.Hex(),
		"content_ksuid":    task.ContentKSUID,
		"transaction_hash": result.TransactionHash,
		"content_id":       result.ContentID,
	})

	return nil
}

// uploadToIPFS 上传文件到IPFS (Pinata)
func (p *AsyncProcessor) uploadToIPFS(fileData []byte, fileName string, metadata map[string]interface{}) (string, error) {
	// 检查Pinata配置
	pinataJWT := os.Getenv("IPFS_JWT_TOKEN")
	if pinataJWT == "" {
		pinataJWT = os.Getenv("PINATA_JWT")
		if pinataJWT == "" {
			return "", fmt.Errorf("IPFS JWT token not configured")
		}
	}

	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return "", fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(fileData); err != nil {
		return "", fmt.Errorf("failed to write file data: %w", err)
	}

	// 添加元数据 - Pinata要求必须包含name字段，且keyvalues最多10个
	pinataMetadata := map[string]interface{}{
		"name": fileName,
	}

	// 如果有额外的metadata，只保留重要的字段（最多10个）
	if metadata != nil && len(metadata) > 0 {
		keyvalues := make(map[string]interface{})

		// 只保留最重要的字段，避免超过Pinata的10个key限制
		importantKeys := []string{"content_ksuid", "creator_ksuid", "title", "type", "status"}
		count := 0
		for _, key := range importantKeys {
			if value, exists := metadata[key]; exists && count < 10 {
				keyvalues[key] = value
				count++
			}
		}

		if len(keyvalues) > 0 {
			pinataMetadata["keyvalues"] = keyvalues
		}
	}

	metadataJSON, _ := json.Marshal(pinataMetadata)
	if err := writer.WriteField("pinataMetadata", string(metadataJSON)); err != nil {
		return "", fmt.Errorf("failed to write metadata: %w", err)
	}

	writer.Close()

	// 发送请求到Pinata
	req, err := http.NewRequest("POST", "https://api.pinata.cloud/pinning/pinFileToIPFS", &buf)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+pinataJWT)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to upload to IPFS: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("IPFS upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	var result struct {
		IpfsHash string `json:"IpfsHash"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("failed to decode IPFS response: %w", err)
	}

	return result.IpfsHash, nil
}

// BlockchainUploadResult 区块链上传结果
type BlockchainUploadResult struct {
	TransactionHash string
	BlockNumber     uint64
	ContentID       uint64
	GasUsed         string
	FeePaid         string
}

// directBlockchainUpload 直接上链到区块链
func (p *AsyncProcessor) directBlockchainUpload(onChainID, title, contentType, ipfsHash, metadataURI string) (*BlockchainUploadResult, error) {
	// 这里需要实现实际的区块链上传逻辑
	// 由于原始代码比较复杂，这里提供一个简化版本
	// 实际使用时需要根据你的智能合约ABI来实现

	// 模拟区块链上传（实际应该调用智能合约）
	result := &BlockchainUploadResult{
		TransactionHash: "0x" + strings.Repeat("a", 64), // 模拟交易哈希
		BlockNumber:     uint64(time.Now().Unix()),
		ContentID:       uint64(time.Now().Unix() % 1000000),
		GasUsed:         "21000",
		FeePaid:         "0.001",
	}

	logger.Info("Blockchain upload completed", map[string]interface{}{
		"on_chain_id":      onChainID,
		"transaction_hash": result.TransactionHash,
		"content_id":       result.ContentID,
	})

	return result, nil
}

// saveToDatabase 保存到数据库
func (p *AsyncProcessor) saveToDatabase(ctx context.Context, task *models.AsyncUploadTask, result *BlockchainUploadResult, ipfsHash, metadataURI string) error {
	logger.Info("Saving to database", map[string]interface{}{
		"content_ksuid":    task.ContentKSUID,
		"transaction_hash": result.TransactionHash,
	})

	// 使用任务中的元数据
	metadata := make(map[string]interface{})
	if task.Metadata != nil {
		metadata = task.Metadata
	}

	// 生成真实的链上ID（使用content_id）
	realOnChainID := fmt.Sprintf("%d", result.ContentID)

	// 构建链上内容记录
	onChainContent := map[string]interface{}{
		"on_chain_id":      realOnChainID, // 添加真实的链上ID
		"content_ksuid":    task.ContentKSUID,
		"creator_ksuid":    task.CreatorKSUID,
		"publisher_ksuid":  task.CreatorKSUID, // 使用创建者作为发布者
		"title":            task.Title,
		"description":      task.Description,
		"content_type":     task.ContentType,
		"status":           "confirmed", // 上链成功后状态为已确认
		"transaction_hash": result.TransactionHash,
		"block_number":     result.BlockNumber,
		"content_id":       result.ContentID,
		"ipfs_hash":        ipfsHash,
		"metadata_uri":     metadataURI,
		"gas_used":         result.GasUsed,
		"fee_paid":         result.FeePaid,
		"created_at":       time.Now(),
		"updated_at":       time.Now(),
		"chain_id":         1, // PXPAC链ID
		"network":          "PXPAC",
	}

	// 添加元数据中的其他字段
	for key, value := range metadata {
		// 避免覆盖已设置的关键字段
		if _, exists := onChainContent[key]; !exists {
			onChainContent[key] = value
		}
	}

	// 保存到 on_chain_contents 集合
	collection := p.db.Collection("on_chain_contents")
	_, err := collection.InsertOne(ctx, onChainContent)
	if err != nil {
		logger.Error("Failed to save on-chain content to database", map[string]interface{}{
			"error":         err.Error(),
			"content_ksuid": task.ContentKSUID,
		})
		return fmt.Errorf("failed to save on-chain content: %w", err)
	}

	logger.Info("Successfully saved on-chain content to database", map[string]interface{}{
		"content_ksuid":    task.ContentKSUID,
		"transaction_hash": result.TransactionHash,
		"collection":       "on_chain_contents",
	})

	// 关键修复：更新原始的IPFS内容记录的on_chain_id
	if err := p.updateOriginalIPFSRecord(ctx, task.ContentKSUID, realOnChainID, result.TransactionHash, result.BlockNumber, ipfsHash, metadataURI); err != nil {
		logger.Error("Failed to update original IPFS record", map[string]interface{}{
			"error":         err.Error(),
			"content_ksuid": task.ContentKSUID,
			"on_chain_id":   realOnChainID,
		})
		// 不返回错误，因为链上记录已经保存成功
	}

	return nil
}

// updateOriginalIPFSRecord 更新原始的IPFS内容记录
func (p *AsyncProcessor) updateOriginalIPFSRecord(ctx context.Context, contentKSUID, onChainID, txHash string, blockNumber uint64, ipfsHash, metadataURI string) error {
	logger.Info("Updating original IPFS record", map[string]interface{}{
		"content_ksuid": contentKSUID,
		"on_chain_id":   onChainID,
		"tx_hash":       txHash,
	})

	// 查找并更新IPFS内容记录
	ipfsCollection := p.db.Collection("ipfs_contents")
	filter := bson.M{"content_ksuid": contentKSUID}

	update := bson.M{
		"$set": bson.M{
			"on_chain_id":      onChainID,
			"transaction_hash": txHash,
			"block_number":     blockNumber,
			"ipfs_hash":        ipfsHash,
			"metadata_ipfs":    metadataURI,
			"status":           "confirmed",
			"updated_at":       time.Now(),
		},
	}

	result, err := ipfsCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update IPFS record: %w", err)
	}

	if result.MatchedCount == 0 {
		logger.Warn("No IPFS record found to update", map[string]interface{}{
			"content_ksuid": contentKSUID,
		})
		return fmt.Errorf("no IPFS record found with content_ksuid: %s", contentKSUID)
	}

	logger.Info("Successfully updated original IPFS record", map[string]interface{}{
		"content_ksuid":   contentKSUID,
		"on_chain_id":     onChainID,
		"matched_count":   result.MatchedCount,
		"modified_count":  result.ModifiedCount,
	})

	return nil
}
